#!/usr/bin/env python3
"""
Performance Test for Collaborative Perception System

This script tests the real-time performance of the collaborative perception
GNN model without GUI to measure actual latency metrics.
"""

import os
import sys
import time
import torch
import yaml
import numpy as np
from collections import deque

# Add the gnn_training directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'gnn_training'))

from model import create_model
import torch.nn as nn
from torch_geometric.nn import GATv2Conv, global_mean_pool, global_max_pool, BatchNorm
from data import OccupancyDataset
from torch_geometric.loader import DataLoader as GeometricDataLoader

class LegacyGATv2Model(nn.Module):
    """Legacy GATv2 model that matches the saved checkpoint structure."""
    
    def __init__(self, input_dim=16, hidden_dim=64, num_layers=3, heads=4, dropout=0.2):
        super().__init__()
        
        self.embedding = nn.Linear(input_dim, hidden_dim)
        
        self.convs = nn.ModuleList()
        for i in range(num_layers):
            self.convs.append(
                GATv2Conv(hidden_dim, hidden_dim // heads, heads=heads, dropout=dropout, concat=True)
            )
        
        self.batch_norms = nn.ModuleList()
        for i in range(num_layers):
            self.batch_norms.append(BatchNorm(hidden_dim))
        
        self.mlp = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 1)
        )
        
    def forward(self, x, edge_index, batch):
        x = self.embedding(x)
        
        for conv, bn in zip(self.convs, self.batch_norms):
            x = conv(x, edge_index)
            x = bn(x)
            x = torch.relu(x)
        
        x_mean = global_mean_pool(x, batch)
        x_max = global_max_pool(x, batch)
        x = torch.cat([x_mean, x_max], dim=1)
        
        x = self.mlp(x)
        return x

def load_model(model_path, config_path):
    """Load the trained model."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    checkpoint = torch.load(model_path, map_location=device)
    
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
        if 'embedding.weight' in state_dict:
            model = LegacyGATv2Model(
                input_dim=config['model']['input_dim'],
                hidden_dim=config['model']['hidden_dim'],
                num_layers=config['model']['num_layers'],
                heads=config['model']['attention_heads'],
                dropout=config['model']['dropout']
            )
            model.load_state_dict(state_dict)
        else:
            model = create_model(config)
            model.load_state_dict(state_dict)
    else:
        model = create_model(config)
        model.load_state_dict(checkpoint)
    
    model.to(device)
    model.eval()
    return model, config, device

def test_performance(model, data_loader, device, num_samples=100):
    """Test the performance of the model."""
    print(f"\n{'='*60}")
    print("PERFORMANCE TESTING")
    print(f"{'='*60}")
    
    preprocessing_times = []
    inference_times = []
    total_times = []
    
    sample_count = 0
    
    with torch.no_grad():
        for batch_idx, data in enumerate(data_loader):
            if sample_count >= num_samples:
                break
                
            # Start total timing
            start_total = time.time()
            
            # Preprocessing timing
            start_preprocess = time.time()
            data = data.to(device)
            data.x = data.x.float()
            data.edge_index = data.edge_index.long()
            preprocess_time = (time.time() - start_preprocess) * 1000
            
            # Inference timing
            start_inference = time.time()
            predictions = model(data.x, data.edge_index, data.batch)
            probabilities = torch.sigmoid(predictions)
            inference_time = (time.time() - start_inference) * 1000
            
            total_time = (time.time() - start_total) * 1000
            
            # Store timings
            preprocessing_times.append(preprocess_time)
            inference_times.append(inference_time)
            total_times.append(total_time)
            
            sample_count += 1
            
            # Print progress every 20 samples
            if sample_count % 20 == 0:
                print(f"Processed {sample_count}/{num_samples} samples...")
    
    # Calculate statistics
    avg_preprocess = np.mean(preprocessing_times)
    avg_inference = np.mean(inference_times)
    avg_total = np.mean(total_times)
    
    p95_total = np.percentile(total_times, 95)
    p99_total = np.percentile(total_times, 99)
    
    print(f"\n📊 PERFORMANCE RESULTS:")
    print(f"{'='*40}")
    print(f"Samples Processed: {sample_count}")
    print(f"Device: {device}")
    print(f"\nTIMING BREAKDOWN:")
    print(f"  Preprocessing: {avg_preprocess:.1f}ms (avg)")
    print(f"  Inference:     {avg_inference:.1f}ms (avg)")
    print(f"  Total:         {avg_total:.1f}ms (avg)")
    print(f"\nLATENCY DISTRIBUTION:")
    print(f"  95th percentile: {p95_total:.1f}ms")
    print(f"  99th percentile: {p99_total:.1f}ms")
    print(f"\nREAL-TIME PERFORMANCE:")
    print(f"  Target Latency: 25ms")
    print(f"  Status: {'✅ ACHIEVED' if avg_total < 25 else '❌ EXCEEDED'}")
    print(f"  FPS Capability: {1000/avg_total:.1f}")
    print(f"  Real-time Margin: {25 - avg_total:.1f}ms")
    
    return {
        'avg_preprocess': avg_preprocess,
        'avg_inference': avg_inference,
        'avg_total': avg_total,
        'p95_total': p95_total,
        'p99_total': p99_total,
        'fps': 1000/avg_total,
        'real_time_achieved': avg_total < 25
    }

def main():
    """Main function."""
    model_path = 'models/checkpoints_standard_gatv2_t3/model_temporal_3_best.pt'
    config_path = 'models/checkpoints_standard_gatv2_t3/config.yaml'
    data_path = 'data/graph_frames'
    
    print("🚀 COLLABORATIVE PERCEPTION PERFORMANCE TEST")
    print("Standard GATv2 T3 Model")
    
    # Load model
    print("\n📥 Loading model...")
    model, config, device = load_model(model_path, config_path)
    print(f"✅ Model loaded successfully on {device}")
    
    # Load test data
    print("\n📊 Loading test data...")
    dataset = OccupancyDataset(
        root=data_path,
        split='test',
        temporal_window=config['data']['temporal_windows'][0],
        binary_mapping=config['data']['binary_mapping']
    )
    
    data_loader = GeometricDataLoader(
        dataset,
        batch_size=1,
        shuffle=False,
        num_workers=0
    )
    
    print(f"✅ Loaded {len(dataset)} test samples")
    
    # Run performance test
    results = test_performance(model, data_loader, device, num_samples=100)
    
    print(f"\n🎯 CONCLUSION:")
    if results['real_time_achieved']:
        print("✅ System meets real-time requirements for warehouse robotics!")
        print(f"   Ready for 40 FPS collaborative perception at {results['avg_total']:.1f}ms latency")
    else:
        print("❌ System exceeds real-time latency target")
        print(f"   Current latency: {results['avg_total']:.1f}ms (target: 25ms)")
    
    return 0

if __name__ == "__main__":
    exit(main())
