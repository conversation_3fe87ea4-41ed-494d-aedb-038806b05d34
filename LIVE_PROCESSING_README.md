# Live ROS Bag Processor for Collaborative Perception

This document describes the live ROS bag processing system that reads data from existing ROS bags, applies preprocessing (synchronization, coordinate transformation, filtering, graph conversion), and performs real-time inference for occupancy prediction.

## Overview

The live processor implements the complete collaborative perception pipeline:

1. **Data Synchronization** (~2ms): Temporal alignment of multi-robot sensor data
2. **Coordinate Transformation** (~3ms): Transform to global coordinate frame using Vicon data
3. **Point Cloud Filtering** (~6ms): Arena bounds filtering and noise removal
4. **Graph Conversion** (~16ms): Convert point clouds to graph structure for GNN
5. **Model Inference** (~5ms): GNN inference for occupancy prediction

**Total Target Latency: 25ms (40 FPS)**

## Files

- `live_rosbag_processor.py`: Main live processing script
- `test_rosbag_processor.py`: Test suite and validation script
- `LIVE_PROCESSING_README.md`: This documentation

## Prerequisites

### System Requirements
- Ubuntu 20.04/22.04 with ROS2 (Humble/Foxy)
- Python 3.8+
- CUDA-capable GPU (optional, CPU inference supported)

### Python Dependencies
```bash
pip install torch torch-geometric numpy pandas matplotlib rclpy sensor_msgs_py
```

### ROS2 Setup
```bash
source /opt/ros/humble/setup.bash  # or your ROS2 distribution
```

## Usage

### 1. Test the System

First, run the test suite to validate your setup:

```bash
python test_rosbag_processor.py --rosbag /path/to/your/rosbag --model models/checkpoints_standard_gatv2_t3/model_temporal_3_best.pt
```

This will:
- Check all dependencies
- Analyze your ROS bag contents
- Test model loading
- Validate the processing pipeline

### 2. Run Live Processing

After tests pass, run the live processor:

```bash
python live_rosbag_processor.py \
    --rosbag /path/to/your/rosbag \
    --model models/checkpoints_standard_gatv2_t3/model_temporal_3_best.pt \
    --voxel-size 0.1
```

### 3. Command Line Options

#### Required Arguments
- `--rosbag`: Path to ROS bag directory
- `--model`: Path to trained model checkpoint (.pt file)

#### Optional Arguments
- `--config`: Model config file (auto-detected from model directory)
- `--voxel-size`: Voxel size for graph conversion (default: 0.1m)
- `--temporal-window`: Temporal window size (default: 3)
- `--no-viz`: Disable real-time visualization
- `--quiet`: Disable verbose timing output
- `--playback-rate`: ROS bag playback rate (default: 1.0x)
- `--arena-bounds`: Arena bounds as x_min x_max y_min y_max (default: 0 20.7 0 9.92)

## Expected ROS Bag Topics

The processor expects the following topics in your ROS bag:

### Required Topics
- `/ep03/ti_mmwave/radar_scan_pcl` (sensor_msgs/PointCloud2): Robot 1 radar data
- `/ep03/vicon/pose` (geometry_msgs/TransformStamped): Robot 1 pose from Vicon

### Optional Topics (for multi-robot)
- `/ep05/ti_mmwave/radar_scan_pcl` (sensor_msgs/PointCloud2): Robot 2 radar data
- `/ep05/vicon/pose` (geometry_msgs/TransformStamped): Robot 2 pose from Vicon

### Additional Topics (informational)
- `/ep03/imu` (sensor_msgs/Imu): IMU data
- `/ep03/odom` (nav_msgs/Odometry): Odometry data
- `/ep03/camera_raw` (sensor_msgs/Image): Camera data

## Real-Time Visualization

When visualization is enabled (default), the system displays:

1. **Point Cloud Data**: Raw radar detections in global coordinates
2. **Graph Structure**: Node connectivity and spatial relationships
3. **Occupancy Predictions**: Heatmap of predicted occupancy probabilities
4. **Processing Performance**: Real-time latency monitoring

## Performance Monitoring

The system provides detailed performance metrics:

```
PIPELINE BREAKDOWN:
  Synchronization:      2.1ms
  Coordinate Transform: 3.2ms
  Point Cloud Filter:   5.8ms
  Graph Conversion:    16.4ms
  Model Inference:      4.9ms
  TOTAL LATENCY:       32.4ms

REAL-TIME CAPABILITY:
  Target: 25ms (40 FPS)
  Achieved: 30.8 FPS
  Status: ❌ TOO SLOW
  Margin: -7.4ms
```

## Troubleshooting

### Common Issues

1. **ROS2 not found**
   ```bash
   source /opt/ros/humble/setup.bash
   export ROS_DOMAIN_ID=0
   ```

2. **Model loading errors**
   - Ensure the model path and config.yaml are correct
   - Check that the model was trained with compatible PyTorch version

3. **No radar topics found**
   - Verify your ROS bag contains the expected topics
   - Use `ros2 bag info /path/to/rosbag` to check contents

4. **Performance too slow**
   - Reduce voxel size (increases processing speed, reduces accuracy)
   - Disable visualization with `--no-viz`
   - Use GPU acceleration if available

5. **Memory issues**
   - The system automatically cleans old data every 5 seconds
   - Reduce temporal window size if needed

### Debug Mode

For debugging, you can:

1. Run with visualization disabled: `--no-viz`
2. Slow down playback: `--playback-rate 0.5`
3. Check ROS bag contents: `ros2 bag info /path/to/rosbag`

## Example Usage with Your Data

Based on your ROS bag metadata, here's how to process your data:

```bash
# Test first
python test_rosbag_processor.py \
    --rosbag /path/to/20250219_174928 \
    --model models/checkpoints_standard_gatv2_t3/model_temporal_3_best.pt

# Run live processing
python live_rosbag_processor.py \
    --rosbag /path/to/20250219_174928 \
    --model models/checkpoints_standard_gatv2_t3/model_temporal_3_best.pt \
    --arena-bounds 0 20.7 0 9.92
```

Your ROS bag contains:
- 2,854 radar messages (sensor_msgs/PointCloud2)
- 14,716 Vicon pose messages (geometry_msgs/TransformStamped)
- Duration: ~6.3 minutes
- Robot: ep03

## Performance Expectations

With your hardware setup, expect:
- **Preprocessing**: ~12ms (sync + transform + filter)
- **Graph Conversion**: ~16ms (realistic for point cloud density)
- **Model Inference**: ~5ms (depends on GPU/CPU)
- **Total**: ~33ms (30 FPS) - close to real-time target

## Next Steps

1. Run the test suite to validate your setup
2. Start with visualization enabled to see the processing pipeline
3. Optimize parameters (voxel size, arena bounds) for your specific use case
4. Consider GPU acceleration for better performance
5. Integrate with your existing warehouse robotics system
