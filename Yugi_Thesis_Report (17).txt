TU DORTMUND UNIVERSITY, DORTMUND, GERMANY
Faculty of Mechanical Engineering

Chair of Material Handling and Warehousing

Master Thesis
Development of a Framework for Collaborative Perception Management Layer
for Future 6G-Enabled Robotic Systems

First Examiner: Prof.’in Dr.-Ing. <PERSON>
Second Examiner: <PERSON><PERSON><PERSON>, M.Sc.
Presented by: <PERSON><PERSON><PERSON><PERSON><PERSON>
Matriculation number: 241940
Course: Master of Science in Automation and Robotics
Issue date: 06. 01. 2025
Submission date: 07. 07. 2025

Dortmund, Germany

Abstract
Robotic perception systems in modern warehouse automation face previously unknown challenges due to blind spots, occlusions, and the dynamic environmental conditions common in
industrial logistics facilities. This thesis creates and assesses a thorough framework for collaborative perception management in warehouse robotics using mmWave radar technology, as a
proof of concept for upcoming 6G-enabled integrated sensing and communication capabilities.
This research presents a comprehensive evaluation of a complete framework for collaborative
perception management in a swarm of AMRs. The proof of concept for 6G employs mmWave
radar as its principal sensor technology, using that as an indirect path for the evaluation of the
future integrated sensing and communication (ISAC) capabilities of the 6G wireless standard.
The thesis presents a novel preprocessing pipeline that turns unrefined collaborative sensor
data into structured graph representations that can be processed by Graph Neural Networks.
Fundamental issues such as spatial alignment across robot-centric coordinate frames, temporal
synchronization between heterogeneous sensors, and the construction of collaborative features
that specifically capture two robot sensor fusion patterns are all addressed by this methodology.
The framework uses high-precision Vicon motion capture ground truth in realistic warehouse
scenarios to process synchronized mmWave radar point clouds from two autonomous robot
platforms.
A systematic assessment of six different GNN architectures establishes clear performance hierarchies for collaborative robot occupancy prediction. This work compares two families of network architectures, Graph Attention Networks version 2 (GATv2), and Edge-Conditioned Convolution (ECC). They are evaluated across two temporal configurations that involve 3-frames
and 5-frames.
The experimental verification includes 34 recording sessions that produce collaborative graph
frames. These frames extend over three warehouse layouts, constituting a complete dataset for
collaborative graph-based perception. Graph Neural Networks can learn authentic dual-robot
sensor fusion patterns instead of single-robot observations through novel collaborative features
that quantify individual robot contributions within shared spatial voxels.
Key contributions are the creation of a preprocessing framework for collaborative robotics research that can be used in further research, and the systematic architectural evaluation of GNNs
for collaborative perception. The successful demonstration of mmWave radar capabilities provides a strong basis for 6G ISAC paradigms, placing collaborative robotics at the core of nextgeneration autonomous warehouse systems.

Acknowledgements
I extend my heartfelt gratitude to all those who have contributed to the successful completion
of this thesis on Development of a Framework for Collaborative Perception Management Layer
for Future 6G-Enabled Robotic Systems.
First and foremost, I would like to express my profound appreciation to my supervisors, Prof.’in
Dr.-Ing. Alice Kirchheim and Irfan Fachrudin Priyanta, M.Sc., whose exceptional guidance and
mentorship have been instrumental throughout this research journey. Their expertise in collaborative robotics and warehouse automation provided invaluable insights that shaped the direction
and quality of this work. The constructive feedback during our regular meetings significantly
enhanced the rigor and clarity of this thesis, while their encouragement to explore innovative
approaches to Graph Neural Network architectures has been truly inspiring.
I am deeply grateful to the Chair of Material Handling and Warehousing (FLW) at TU Dortmund University for providing me with this remarkable research opportunity and access to
state-of-the-art facilities. The experimental infrastructure, including the Robomaster platforms,
mmWave radar sensors, and Vicon motion capture system, enabled the comprehensive data collection and validation that forms the backbone of this thesis.
My sincere appreciation extends to my fellow researchers and colleagues at the FLW who contributed to meaningful discussions and provided technical assistance during the experimental
phases. I would also like to acknowledge the broader research community whose pioneering
work in collaborative perception, and warehouse automation laid the foundation for this thesis.
Special thanks go to my family for their unwavering support and understanding throughout
the demanding periods of this research. Their encouragement during challenging phases of
data processing and model development provided the emotional strength necessary to persevere
through complex technical obstacles. I am also grateful to my friends who provided valuable
perspectives and helped clarify complex concepts through constructive discussions.
Finally, I acknowledge the contributions of the open-source community whose software frameworks, including PyTorch Geometric and ROS, made the implementation of sophisticated Graph
Neural Network architectures possible. This thesis represents not only my individual effort but
also the collective support and contributions of all these remarkable individuals and institutions.

I

Contents
1

2

3

4

Introduction
1.1 Technological Context and Challenges
1.2 Problem Statement . . . . . . . . . .
1.3 Research Objectives and Contributions
1.4 Thesis Organization . . . . . . . . . .

.
.
.
.

.
.
.
.

.
.
.
.

.
.
.
.

.
.
.
.

.
.
.
.

.
.
.
.

.
.
.
.

.
.
.
.

.
.
.
.

.
.
.
.

1
2
3
3
4

Fundamentals
2.1 mmWave Radar Sensing Fundamentals . . . . . . . . . . . .
2.1.1 Range Measurement . . . . . . . . . . . . . . . . .
2.1.2 Velocity and Angle Measurement . . . . . . . . . .
2.1.3 Radar Signal Processing and Point Cloud Generation
2.2 Graph Neural Network Concepts . . . . . . . . . . . . . . .
2.2.1 Why GNNs for Collaborative Perception? . . . . . .
2.2.2 General Mechanisms in GNNs . . . . . . . . . . . .

.
.
.
.
.
.
.

.
.
.
.
.
.
.

.
.
.
.
.
.
.

.
.
.
.
.
.
.

.
.
.
.
.
.
.

.
.
.
.
.
.
.

.
.
.
.
.
.
.

.
.
.
.
.
.
.

.
.
.
.
.
.
.

.
.
.
.
.
.
.

5
5
5
7
9
10
10
11

.
.
.
.
.

.
.
.
.
.

.
.
.
.
.

.
.
.
.
.

.
.
.
.
.

.
.
.
.
.

.
.
.
.
.

.
.
.
.
.

.
.
.
.
.

.
.
.
.
.

13
13
13
15
15
16

. . . . . . . . . . . . . . . . .
. . . . . . . . . . . . . . . . .

17
17

.
.
.
.

.
.
.
.

.
.
.
.

.
.
.
.

.
.
.
.

State of the Art
3.1 Warehouse Automation . . . . . . . . . . . . .
3.2 Collaborative Perception Frameworks . . . . .
3.3 mmWave Radar for Robotic Perception . . . .
3.4 Graph Neural Networks for Spatial Reasoning .
3.5 SLAM and Mapping . . . . . . . . . . . . . .
3.6 Communication Infrastructure for Collaborative
Robotics . . . . . . . . . . . . . . . . . . . . .
3.7 Research Gaps and Opportunities . . . . . . . .

.
.
.
.

.
.
.
.
.

.
.
.
.

.
.
.
.
.

.
.
.
.

.
.
.
.
.

Methodology
4.1 Data Preprocessing Framework . . . . . . . . . . . .
4.1.1 Raw Data Extraction and Standardization . .
4.1.2 Data Synchronization . . . . . . . . . . . . .
4.1.3 Coordinate Transformation . . . . . . . . . .
4.1.4 Data Filtering and Quality Enhancement . . .
4.1.5 Data Labeling and Ground Truth Generation
4.1.6 Graph Structure Generation . . . . . . . . .
4.2 Graph Neural Network . . . . . . . . . . . . . . . .
4.2.1 Graph Feature Representation . . . . . . . .
4.2.2 Architectural Selection Rationale . . . . . .
4.2.3 Temporal Integration . . . . . . . . . . . . .
4.2.4 Class Imbalance Handling . . . . . . . . . .
4.3 Evaluation Framework for Collaborative Perception .

II

.
.
.
.

.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.

.
.
.
.

.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.

.
.
.
.

.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.

.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.

19
20
20
20
22
23
24
25
27
28
28
32
33
34

III

CONTENTS
4.3.1
4.3.2
4.3.3

5

6

Confusion Matrix Analysis . . . . . . . . . . . . . . . . . . . . . . . .
Classification Evaluation Metrics . . . . . . . . . . . . . . . . . . . .
Spatial Accuracy Evaluation . . . . . . . . . . . . . . . . . . . . . . .

Experiments and Results
5.1 Experimental Setup . . . . . . . . . . . . . . . . . . . . . . . . . . . .
5.1.1 Warehouse Environment . . . . . . . . . . . . . . . . . . . . .
5.1.2 Robotic Platform Configuration . . . . . . . . . . . . . . . . .
5.1.3 Data Collection . . . . . . . . . . . . . . . . . . . . . . . . . .
5.2 Preprocessing Implementation . . . . . . . . . . . . . . . . . . . . . .
5.2.1 Graph Construction . . . . . . . . . . . . . . . . . . . . . . . .
5.2.2 Temporal Integration . . . . . . . . . . . . . . . . . . . . . . .
5.2.3 Dataset Partitioning . . . . . . . . . . . . . . . . . . . . . . . .
5.3 GNN Architecture Implementation . . . . . . . . . . . . . . . . . . . .
5.3.1 Standard GATv2 Architecture Implementation . . . . . . . . .
5.3.2 Complex GATv2 Architecture Implementation . . . . . . . . .
5.3.3 ECC Architecture Implementation . . . . . . . . . . . . . . . .
5.3.4 Training Infrastructure . . . . . . . . . . . . . . . . . . . . . .
5.3.5 Architecture Comparison Summary . . . . . . . . . . . . . . .
5.4 Comprehensive Model Evaluation and Analysis . . . . . . . . . . . . .
5.4.1 Confusion Matrix Analysis . . . . . . . . . . . . . . . . . . . .
5.4.2 Classification Metrics . . . . . . . . . . . . . . . . . . . . . .
5.4.3 Spatial Accuracy Results . . . . . . . . . . . . . . . . . . . . .
5.4.4 Temporal Window Comparative Analysis . . . . . . . . . . . .
5.4.5 Computational Efficiency and Real-Time Performance Analysis

34
35
35

.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.

37
37
37
38
39
41
45
47
47
49
49
50
51
52
53
53
53
55
60
61
62

Conclusion and Future Work
6.1 Summary of Contributions and Research Impact . . . . . . . . . . . . . . . . .
6.2 Future Work and Research Directions . . . . . . . . . . . . . . . . . . . . . .
6.3 Concluding Remarks . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

64
64
65
66

Bibliography

.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.

.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.
.

I

List of Figures

VII

List of Tables

VIII

List of Abbreviations
Affidavit

IX
X

1

Introduction

The world of logistics has changed significantly in recent times. In particular, warehouses
have emerged from being simple areas to store things to being complex automated hubs that
are the backbone of global supply chains. These contemporary structures use Cyber-Physical
Production Systems (CPPS) to seamlessly combine digital intelligence with physical processes.
This makes it possible for a swarm of Autonomous Mobile Robots (AMR) to move around
in complicated spaces like high-bay storage spaces, mobile workstations, and places where
humans work with machines [1]. This evolution is a big step away from traditional manual
operations and towards fully autonomous logistics systems that need to keep up with efficiency
standards in environments that are getting more and more difficult.
For years, warehouse automation has primarily depended on single-agent robotic systems performing independently in these intricate environments. These systems, remarkable for their successes, eliminate many harmful warehouse practices by integrating sophisticated sensor suites
that are mainly comprised of cameras, LiDAR sensors, and radar technologies. These allow
individual robots to create comprehensive environmental models by performing advanced sensor fusion techniques. [2]. The core architecture of these systems is centred on autonomous
decision-making, in which each robot navigates using only its own perception abilities and processes sensory data locally, negating the need for coordination or information exchange with
other robots [3]. This method has worked well in controlled settings with predictable layouts
and little fluctuation in the surroundings.
Nevertheless, the operational requirements of modern warehouse settings have started to highlight notable drawbacks of single-agent strategies. Modern facilities have high-bay configurations that produce complicated occlusion patterns, increasingly narrow aisles designed to
maximise storage density, frequent layout changes to accommodate shifting inventory requirements, and continuous movement of employees, machinery, and goods [4]. Individual robotic
systems that must rely solely on their sensor capabilities to perceive and react to rapidly changing environmental conditions face challenges as a result of these dynamic conditions. When
robots come across situations that call for quick identification and avoidance of both static infrastructure components, like shelving units, and dynamic obstacles, like moving individuals
and machinery, the complexity increases.
When analysing the performance characteristics of individual sensor technologies under demanding warehouse conditions, the limitations become specifically evident. Despite offering
rich visual information, camera-based systems perform poorly in settings with changing lighting, in regions hidden by visual obstacles, or in places where the accuracy of depth perception
is compromised by repetitive structural elements . Despite their generally dependable performance, LiDAR sensors face challenges in dusty environments, which are typical of warehouse
operations; areas with highly reflective surfaces that produce false readings; and extremely
1

2

Chapter 1 - Introduction

crowded spaces where it becomes challenging to interpret and process dense point cloud data
efficiently [5]. The isolated sensing capabilities of individual robots are insufficient to compensate for these sensor-specific vulnerabilities, which result in critical blind spots and perception
gaps.
These basic limits of single-agent methods have led to the emergence of the collaborative perception approach. This new method enables robots to collect and share sensory data and work
together to form a much more complete model of their environment which goes beyond the
capabilities of any individual agent [6]. By combining sensory data from various platforms, this
approach essentially shifts the perception problem from one of individual sensor limitations to
one of distributed sensing capabilities, producing a single environmental model with greatly
fewer blind spots and improved situational awareness [5]. The collaborative framework makes
use of the idea that information exchange between robotic agents can get around the limitations
imposed by independent sensors and compuational resources.

1.1

Technological Context and Challenges

The effectiveness of collaborative perception fundamentally depends on two critical technological components: robust sensing capabilities and a reliable communication infrastructure. Although conventional sensors, such as cameras and LiDAR, have proven effective in controlled
environments, they face limitations in the real-world setting of a warehouse. Cameras have
trouble with different lighting and obstacles, while LiDAR systems face challenges with dust,
shiny surfaces, and the heavy clutter found in warehouses. On the other hand, millimeter-wave
radar sensors have shown great potential for warehouse use as a proof-of-concept for 6G [7].
They provide strong sensing abilities even when optical and laser-based sensors do not work
well. Operating in the 60-64 GHz frequency band for industrial purposes, mmWave radar delivers dependable performance in tough situations such as dust, smoke, changing lighting, and
partial blockages.
Collaborative perception can only reach its full potential with the right infrastructure for communication. Early wireless technologies like Wi-Fi have not met the tough requirements for
real-time coordination among robotic platforms in busy warehouse environments. Early wireless technologies such as Wi-Fi have proven insufficient for the demanding requirements of
real-time coordination among robotic platforms in dense warehouse environments [7]. While
5G technology has made major strides in latency, bandwidth, and reliability, it still does not meet
the ultra-reliable low-latency communication standards needed for safety-critical robotic applications in fast-changing warehouse settings. The arrival of 6G technologies aims to solve these
communication challenges with improvements in wireless networking. Beyond just enhancing
5G specifications, 6G introduces groundbreaking ideas like Integrated Sensing and Communication (ISAC), which combines sensing and communication into a single framework.[8]. This
is especially important for mmWave-based systems, where the same hardware can handle both
high-resolution sensing and high-bandwidth communication, enabling effective collaborative
perception in tight spaces.[9].

3

Chapter 1 - Introduction

1.2

Problem Statement

Critical gaps remain in the development of efficient collaborative perception systems specific
for warehouse robotics applications, despite notable advancements in individual robotic perception capabilities and the encouraging potential of collaborative approaches. Current collaborative frameworks, which were mainly created for automotive applications, do not take into
account the particular difficulties that arise in indoor warehouse settings. These difficulties include the need for constant adaptation to shifting inventory layouts and operational patterns,
complex three-dimensional storage structures, higher agent density requirements, and frequent
occlusions brought on by storage infrastructure [10]. Current frameworks fall short in addressing the fundamental differences between indoor warehouse operations and outdoor automotive
scenarios, which call for specialised approaches.
Integrating mmWave radar technology into collaborative robotic systems for warehouse applications is an area not explored very much in current research. The challenges of merging sparse
radar point clouds from various mobile platforms with varying perspectives, motion dynamics, and temporal synchronisation requirements are not addressed by current radar processing
methods, which primarily concentrate on single-platform applications. To enable the practical
deployment of radar-based collaborative perception systems in warehouse environments, this
presents a significant research gap that needs to be solved.
Existing perception approaches generally treat sensing, communication, and decision-making
as separate problems. This division misses opportunities for cross-layer optimization, which
could greatly increase the overall system performance. ISAC technologies in 6G networks offer
a potential pathway to provide such integration, but the development of practical frameworks
that make effective use of these capabilities in the context of warehouse robotics remains undeveloped [11].

1.3

Research Objectives and Contributions

This thesis addresses these challenges by introducing a structured framework for collaborative
perception in warehouse robotics that leverages the complementary strengths of mmWave radar
sensing and emerging 6G communication technologies. The ultimate objective is to bridge the
gap between current collaborative perception frameworks and the unique operational demands
of modern warehouse environments.
The specific research objectives are:
1. Conducting a comprehensive analysis of state-of-the-art collaborative perception frameworks functions and identifying major limitations in their application in warehouse automation, particularly through mmWave radar technology.
2. Designing a novel preprocessing pipeline to transform raw sensor readings across different robotic platforms to structured representations suitable for collaborative perception
algorithms.
3. Developing and validating Graph Neural Network (GNN) architectures specially designed
for spatial reasoning in collaborative robotics applications.

4

Chapter 1 - Introduction
4. Building a perception dataset from the Robomaster platform mounted with mmWave realistic warehouse settings.
5. Formulating a comprehensive evaluation framework that measures model performance
across different dimensions like spatial accuracy and temporal consistency.

The core contributions of this thesis are the creation of a preprocessing pipeline that addresses
the intrinsic challenges in the radar data fusion from different robotic platforms, such as temporal synchronization, coordinate conversion, and data labeling requirements. The thesis proposes the systematic analysis of Graph Neural Network architectures for collaborative robot
occupancy prediction by comparing six various models to establish absolute performance hierarchies and architecture principles.

1.4

Thesis Organization

The rest of this thesis is organized to provide a detailed review at collaborative perception in
warehouse robotics. Chapter 2 lays out the basic theoretical background, covering mmWave
radar principles, platform specifications, and Graph Neural Networks. Chapter 3 offers a complete review of the latest developments in collaborative perception, warehouse automation, and
related technologies. It also highlights key research gaps that creates a foundation for this work.
Chapter 4 details the proposed methodology, which includes a thorough preprocessing pipeline
and the design of the Graph Neural Network models created for this application. Chapter 5
describes the experimental setup, data collection methods, and implementation details. It also
includes an evaluation of results that features performance comparisons, spatial analysis, and
insights on the architecture. Finally, Chapter 6 wraps up the thesis by summarizing the main
contributions and suggesting future research directions that could build on the foundations established here.
This research marks an important step toward achieving fully autonomous warehouse operations
through collaborative perception technologies. It offers both theoretical insights and practical
solutions for using coordinated robotic systems in complex industrial settings. By focusing on
the key areas of sensing, communication, and machine learning technologies, this work contributes to the larger goals of Industry 4.0 and the development of smart, flexible manufacturing
and logistics systems that can handle the needs of modern global supply chains.

2

Fundamentals

This chapter presents the basic theories that are crucial for comprehending how collaborative
perception works in modern robotic systems. The foundations include millimeter-wave radar
sensing concepts and graph neural network structures, which serve as a basis for the methodological contributions in later chapters. Gaining an understanding of these fundamental ideas is
essential to creating collaborative perception frameworks that work well in dynamic warehouse
settings where conventional sensing modalities frequently encounter major difficulties.

2.1

mmWave Radar Sensing Fundamentals

Millimeter-wave (mmWave) radar systems, particularly that works in the 60-64 GHz bands,
have become vital to technologies for working in industrial and robotic applications[12]. Unlike optical sensors that can struggle with various lighting conditions and unlike LiDAR systems
that can also be affected by dust and surfaces that reflect light, mmWave radar provides a solid
base of sensing capabilities with a high degree of reliability that makes it a leading candidate
for robotic applications in adverse environments. [13]. The capability of the sensor to simultaneously measure range, velocity, and angle through dust, smoke and other visual blockages
makes mmWave radar an ideal sensor for autonomous warehouse and industrial applications.
The Frequency Modulated Continuous Wave (FMCW) radar architecture has been common
in this sector due to its high range resolution and cost-effectiveness in terms of computation.
Compared to pulsed radar systems, FMCW radar continuously sends electromagnetic waves,
which enable accurate measurements at modestly low power consumption and fewer hardware
requirements. This continuous process enables the possibility of extracting rich environmental
information through sophisticated signal processing techniques that transform raw electromagnetic reflections into consumable perception data [14].

2.1.1

Range Measurement

The fundamental principle underlying the operation of FMCW radar involves the transmission
of frequency modulated continuous waves, and the analysis of the received signals that are
returned from the targets, to extract the information about the targets. The signals that are
transmitted in FMCW systems are continuous waves whose frequency varies linearly with time.
These signals are referred to as chirp signals.By measuring the frequency differences between
transmitted and received signals, this linear frequency modulation allows the radar to determine
the target’s range [14]. Figure 2.1 shows the complete chain of the chirp generation through
5

mated with a

TI has brought innovation to the field of FMCW

value:
by integrating
a DSP,
MCU and the TX
re
accurate when 𝜃𝜃sensing
has a small
value.as shown
in Figure

pends on

6

Chapter 2 - Fundamentals

RF, RX RF, analog and digital components into a

RFCMOS single
chip.
range-Doppler-angle
processing.

a small value.
TX ant.

ero
Synth

s θ approaches 90o

FFT
Signal Processing

ADC

}

		

LP
Filter

IF signal

}
}

l values.

RX ant.

RF

Analog

Digital

Figure 14. RF, analog and digital components of an FMCW sensor.

Figure 2.1: FMCW radar signal processing pipeline [14]

The mathematical foundation of FMCW radar starts from the transmitted chirp signal, whose
7
May 2017
instantaneous frequency varies linearly over the chirp duration. This frequency-time relationship can be detailed as:
B
f (t) = fc + · t
(2.1.1)
Tc
where fc is the starting frequency of the chirp, B indicates the total bandwidth swept during the
chirp duration, and Tc is the time duration of a single chirp. The linear term TBc , often denoted
as the chirp slope S, directly affects the radar’s range resolution capabilities and establishes the
rate of frequency change.
When this chirped signal strikes an object that is at a distance d and travels back to the radar
receiver, it has experienced a round-trip propagation delay. This round-trip time delay, the
foundation of range measurement, is calculable as:
τ=

2d
c

(2.1.2)

where c is the speed of light in the propagation medium. This delay appears as a difference in
frequency between the instantaneous frequency of the transmitted signal and the frequency of
the received echo at any specified moment in time.
The radar receiver’s mixing process brings together the transmitted and received signals, yielding an intermediate frequency (IF) signal whose frequency reflects the range to the target. For
stationary targets, this beat frequency remains constant throughout the chirp duration and is
expressed by the following equation.
fIF =

2Sd
,
c

where S =

B
Tc

(2.1.3)

The target range can be calculated directly from the measured beat frequency by manipulating
this relationship algebraically:
fIF · c · Tc
d=
(2.1.4)
2B
The FMCW radar system’s range resolution, which defines its ability to discriminate between

7

Chapter 2 - Fundamentals

two narrowly spaced targets in the range dimension, is fundamentally constrained by the transmitted chirp’s bandwidth. This resolution is stated by:
∆R =

c
2B

(2.1.5)

The inverse relationship between bandwidth and range resolution underscores a vital design
consideration in FMCW radar systems. Fine range resolution is obtained by larger bandwidth
allocations, which can be restricted both by regulatory limitations as well as hardware limitations [12]. Modern mmWave radar systems typically operate with bandwidths in the several
gigahertz range in order to facilitate centimeter-scale range resolution, and are thus well-suited
to the task of detailed environmental mapping and obstacle detection in robotics applications.
However, with the increasing spread of radar systems in automotive and industrial applications
comes the problem of mutual interference between co-located radar systems operating on the
same frequency bands [15].

2.1.2

Velocity and Angle Measurement

By using the Doppler effect, FMCW radar systems are excellent at measuring target velocity in
addition to range determination [13]. When moving targets reflect electromagnetic waves, the
reflected signal’s frequency shift is caused by the relative motion. The target’s radial velocity
component in relation to the radar can be directly measured through this Doppler frequency
shift.
The Doppler frequency shift for a target travelling at radial velocity v is as follows:
fd =

2v fc
c

(2.1.6)

where fc is the carrier frequency of the radar signal. This frequency shift results from the
compression or expansion of the electromagnetic waves caused by the relative motion between
the radar and the target.
In real-world FMCW applications, phase analysis of several consecutive chirps is used to measure velocity instead of direct frequency measurement. For velocity estimation, the phase difference between subsequent chirps offers a more accurate and computationally effective approach.
The following is an expression for this phase progression:
∆φ =

4πvTc
λ

(2.1.7)

where λ = c/ fc is the wavelength of the radar signal. This approach based on phases allows for
very precise measurements of the velocity even for targets that are moving slowly.
The velocity estimation formula can be obtained by rearranging the phase relationship:
v=

λ ∆φ
4πTc

(2.1.8)

to the frame time (Tf).
8

Chapter 2 - Fundamentals

nal with the horizontal plane, as shown in Figure 10. This
The maximum velocity that can be measured is fundamentally limited by the discrete sampling
of digital radar systems and the intrinsic 2π periodicity of phase measurements. This constraint
on velocity ambiguity is provided by:

r small values.
d signal with the horizontalFigure
plane,12.
as shown
in Figure is
10.more
This accurate for small values.
AoA estimation
λ
(2.1.9)
vmax of
= ±view
bservation that
Maximum angular field

ximum
Angular Field of View
object results

4Tc

The maximum angular field of view of the radar is
AoA
that
the
radar
can
estimate.
Figure between velocity resolution and maximum velocity that
This
constraint
presents a See
compromise
range-FFT or
defined
by
the
maximum
AoAonthat
themaximum
radar
can
maximum angular
field
of
view
of
the
radardesign
is defined
bythethe
AoA that
theinradar
must be carefully weighed in system
based
expected
target
velocity
ranges
the can estim
erform angular
application estimate.
environment.See Figure 13.

ennas as

distance from
-θmax θmax
of arrival.
esults in a phase
ge in theenables
distance of an object results in a phase change
change
perform angular estimation, using at least two radar
RX
gle of arrival.
13. Maximum
angular
field offield
view.
e object to each of theFigure
antennas
in aangular
phase
Figure
2.2:results
Maximum
of view determination [14]
e AoA. in the distance of an object results in a phase change
change
ed to performAngle
angular
estimation,
using
at systems
least two
RX
estimation
for FMCW
radar
makes
of multiple
receive antenna elements
Unambiguous
measurement
of use
angle
requires
view.
in specific
determine the direction of arrival of reflected
m the objectphased
to each
of the geometric
antennasconfigurations
results in a to
phase
180°.used
Using
Equation
16, angular
this
corresponds
to(ULA) where
Figure
13.
Maximum
field
of view.
signals [16].|Dw|
The<majorly
configuration
employs
a uniform
linear
array
te the AoA. antenna elements
2𝜋𝜋𝜋𝜋𝑅𝑅𝜋𝜋𝜋𝜋(𝜃𝜃)
are positioned at equal intervals in a straight line. As the electromagnetic
6, this corresponds
to
<angle
π. θ to the array normalo approaches the antenna array, the wavefront
2𝜋𝜋𝜋𝜋𝑅𝑅𝜋𝜋𝜋𝜋(𝜃𝜃
wavefront with of
specific
𝜆𝜆 angle
mbiguous measurement
requires |∆𝜔𝜔|<180 . Using equation 16, this corresponds to
𝜆𝜆
arrives at the different antenna elements at slightly different instances due to variations in path
Equation
17
shows
that
the
maximum
field
of
view
d
l apart canlengths.
service is:
nas
ation
18 shows that the
maximum
field of spaced
view thatl apart
two antennas
spaced
that
two antennas
can service
is: l apart can service is:

The difference in path length between neighboring antenna elements produces a measurable
phase shift that can be used to estimate the angle. For𝜆𝜆 a ULA with element spacing d, the phase
18)
(17)
𝜃𝜃𝑚𝑚𝑚𝑚𝑚𝑚by:= 𝐴𝐴𝐴𝐴𝐴𝐴−1 ( )
(18)
difference
between elements is given
quired to estimate
AoA.

e AoA.

2𝜋𝜋

gefield
is derived
ar
of view ±90 degrees.
2πd
sin(θ )
A spacing between ∆φ
the two
antennas
of l = l/2
(2.1.10)
=
acing
between
results
inλ the largest angular field of view
±90 degrees.
ant
as Equation 15:the two antennas of 𝑙𝑙 = 𝜆𝜆/2
results in the largest angular field of view ± 90°.
e required
estimate AoA.
(15)toThe
(15)
xas
Instruments
mmWave
Sensor
Solution
angle of arrival
can be estimated
using the
inverse relationship by measuring this phase
difference across
the antenna
array:
Texas
Instruments

mmWave

asof
Equation
15:
dically
angle
nearby objects
by using a
avefront
basic
Å range,
ã velocity and angle of nearby objects by u
shows
Δ𝑑𝑑
= 𝑙𝑙𝐴𝐴𝐴𝐴𝐴𝐴(𝜃𝜃),
where
l is to
thedetermine
distance the
ou canthat
see,
an FMCW
sensor
is able
sensor
solution
λ ∆φant
θ = arcsin
(2.1.11)
m
a measured
withand
Equation
bination
of(15)
RF,∆𝜙𝜙
analog
digital16:
electronic
components.
here
l is the
2πd
As you can see, an FMCW sensor is able to
s the angle of
re 14 is a block
the different
components.
determine
the range,
velocity
of nearbyratio are some of
(16)
Thediagram
number
ofofantenna
elements
N,
spacing,and
and angle
the signal-to-noise
etry
shows
that
Δ𝑑𝑑
=
𝑙𝑙𝐴𝐴𝐴𝐴𝐴𝐴(𝜃𝜃),
where
l
is
thetheir
distance
e measured the
DFvariables that affect the radar system’s
angular resolution, which establishes its capacity to
by using
a combination
of RF, analog and
e from a measured
∆𝜙𝜙objects
with
Equation
discriminate
between
targets at16:
various angles. One way to approximate the theoretical angular
ndency. 𝐴𝐴𝐴𝐴𝐴𝐴(𝜃𝜃)
is approximated
with a linear function
resolution isdigital
as follows:
electronic components.
λ
∆θ ≈
(2.1.12)
(16) (16)
Nd cos(θ )
Figure 14 is a block diagram
of the
accurate when 𝜃𝜃 has a small value.as shown in Figure
components.
dependency.
approximated
with a linear function
s is called a 𝐴𝐴𝐴𝐴𝐴𝐴(𝜃𝜃) isdifferent

oximated with a

TI has brought innovation to the field of FMCW

9

Chapter 2 - Fundamentals

The relationship shows that angular resolution gets better with larger antenna apertures (through
more elements or wider spacing) and gets worse at larger off-boresight angles where cos(θ )
term becomes significant.

Figure 2.3: Texas Instruments IWR6843 ISK Module

Current mmWave radar modules, including the Texas Instruments IWR6843 shown in Figure 2.3, are made with compact parts that integrate antennas for transmitting and receiving
signals and do sophisticated signal processing. [14]. Complex Multiple-Input Multiple-Output
(MIMO) radar configurations that improve angular resolution and allow elevation angle estimation using virtual antenna array techniques are made possible by these integrated solutions.
[16].

2.1.3

Radar Signal Processing and Point Cloud Generation

Sophisticated digital signal processing pipelines that extract valuable information from complex electromagnetic returns are necessary to convert raw radar measurements into actionable
perception data [13]. To extract range, velocity, and angular information, the processing chain
begins with digitising the received IF signals and then moves on to spectral analysis.
Range extraction begins and ends with the use of Fast Fourier Transform (FFT) operations on
the digitized signals. Before the FFT generates the range profile, which has each frequency
bin mapping to a specific range via Equation 2.1.4, windowing functions such as Hamming or
Blackman are used to reduce spectral leakage. The result, range-FFT, is computed for each
chirp to form a range-time matrix.
The range-Doppler map is obtained by performing Doppler-FFT across multiple chirps for each
range bin in order to extract velocity [14]. The number and spacing of chirps determine the
velocity resolution, which strikes a balance between update rates and precision.
Angular information takes advantage of having multiple receive antennas. Phase differences
across the array are used with beamforming methods, such as Bartlett, Capon, or MUSIC, to
estimate angles.[16].
Stages of this process yield radar point clouds that encode the 3D positions and velocities of
the targets being tracked. Each point in the point cloud contains Cartesian coordinates (x, y, z).

10

Chapter 2 - Fundamentals

These coordinates are derived from spherical coordinates (r, θ , φ ) as:
x = r sin(θ ) cos(φ )
y = r sin(θ ) sin(φ )
z = r cos(θ )

(2.1.13)
(2.1.14)
(2.1.15)

Advanced methods such as Constant False Alarm Rate (CFAR) detection adaptively set thresholds for robust detection. Maintaining target identities is the job of multi-target tracking, and
machine learning models refine classification and reduce false alarms.[13, 17].
Careful temporal and spatial alignment is required in sensor fusion, owing to the differing update rates and degree of sparsity in comparison to LiDAR[17, 16]. Real-time robotic applications impose very strict requirements that dictate the use of hardware accelerators such as
FPGAs and DSPs. A device such as the Texas Instruments IWR6843 integrates dedicated accelerators for tasks such as FFT computation, enabling the instrument to produce point clouds
at a sufficiently high rate and low power that makes it useful for dynamic navigation.[14].

2.2

Graph Neural Network Concepts

Graph Neural Networks (GNNs) are a robust class of neural networks that operate on data structured as graphs. They recover dependencies and relationships in such data and have pushed the
state of the art forward in many different domains, including social network analysis, recommendation systems, molecular chemistry, and computer vision. GNNs are one of the most
powerful and effective tools we have today for turning graph-based data into useful insights.
In contrast to conventional convolutional networks that work on standard grid formations (such
as images), GNNs are purpose-built for the type of irregular, non-Euclidean data structures that
radar point clouds, sparse sensor readings, or closely packed arbitrary spatial arrangements of
objects form. That makes GNNs particularly good candidates for addressing robotic perception
challenges, in which the relevant data naturally consists of interconnected entities rather than
rigid grids.

2.2.1

Why GNNs for Collaborative Perception?

GNNs have novel strengths for collaborative perception in their native representation of spatial
relations and ability to handle non-uniform data structures. Radar point clouds, voxelized or
clustered, naturally form sparse, non-uniform graphs wherein vertices are spatial regions and
edges specify proximity relations. The graph-based representation allows GNNs to operate natively on such structures without loss of fundamental geometric and topological information
which would be sacrificed in the conventional grid-based approach. Differing from typical convolutional architectures for uniformly structured grid data from cameras, GNNs are particularly
well-equipped to accommodate the naturally sparse and non-uniform distribution of radar sensor data and learn informative spatial features without dense, uniform scanning of the entire
sensing space.
GNNs’ contextual reasoning capability stems from their sophisticated message passing schemes

11

Chapter 2 - Fundamentals

that enable each encoded spatial region as a node to summarize and process information from its
neighborhood. This environmental knowledge is critical to differentiate between valid objects
and ambient noise and define proper boundaries among filled and unoccupied space in complex
warehouse environments. The paradigm of message passing inherently provides scalability to
dynamic environments where the number and spatial configuration of observed objects can vary
continuously because the graph structure can dynamically change for various numbers of nodes
and edges without undergoing architectural modifications.
In collaborative systems, GNNs show a special ability to accommodate information from many
different sensor sources across several robotic platforms. When we treat the radar voxels and
spatial observations from different robots as nodes in a single graph, GNNs let us perform
collaborative spatial reasoning that extends well beyond any one robot’s limited viewpoint.
This becomes especially valuable in places like warehouses, where robots must coordinate their
perception in order to navigate around dynamic obstacles and other autonomous agents while
maintaining accurate global maps[18].
The majority of GNN architectures are based on the principle of message passing .This framework provides a unified understanding of how GNNs learn node representations through the
iterative exchange of information. The core notion is that each node iteratively collects information from its direct neighbors and combines it with its own features to reformulate its
representation. In order to create complex spatial and contextual representations, this process
is repeated across several layers, enabling nodes to collect data from progressively more distant
parts of the graph.

2.2.2

General Mechanisms in GNNs

While the paradigm of message passing is universal, GNNs use many clever mechanisms to process both spatial and contextual information in systems where collaborative perception occurs.
A key development is adaptive neighbour weighting, in which GNNs dynamically determine the
relative significance of each neighbor’s contribution to the feature update of a particular node.
Since some spatial relationships are more important than others in warehouse environments,
this adaptive mechanism enables the network to selectively focus on the most pertinent spatial
regions or features rather than giving equal weight to all nearby nodes. GNNs can discriminate
between less significant environmental noise and important spatial features, like robot-to-robot
proximity relationships, as a result to their capacity to learn these attention patterns.
The relationship features incorporation represents another vital mechanism through which GNNs
use the attributes of the connections between nodes to make spatial reasoning more capable.
The attributes of edges can include geometric properties like the distance between nodes, relative angles, or even semantic relationships like collaborative robot interactions. GNNs formally
encode these edge properties directly into the message passing model. Such direct relationship
feature modeling allows GNNs to learn high-level spatial dependencies that would be difficult
for node features alone, particularly systems in which geometric and collaborative relations
among perception regions hold critical information for successful occupancy prediction.
Learning to represent the hierarchy of information with multi-layer GNN architectures allows
us to progressively abstract the representations of spatial information from local patterns to
global understanding of the environment. While deeper layers integrate information over increasingly wider receptive fields to develop comprehensive scene understanding, initial layers

12

Chapter 2 - Fundamentals

usually capture fine-grained local spatial patterns and immediate neighbour relationships. Effective coordination in complex warehouse environments is made possible by this hierarchical
processing mechanism, which is crucial for collaborative perception tasks where robots must
simultaneously reason about local spatial occupancy and global environmental context [18].
These broad ideas serve as the conceptual foundation for comprehending how GNNs support
spatial reasoning in collaborative perception systems. The next chapters will discuss in detail
about particular architectural implementations of these mechanisms.
This chapter has set up the foundational theoretical elements that are necessary for grasping
the idea of collaborative perception in collaborative systems. The principles of mmWave radar
provide the sensor-level foundation for good perception of the environment, while the fundamentals of signal processing set up the real mathematical basis for extracting spatial and kinematic information from radar measurements. The Graph Neural Network concepts provide the
computational frameworks for doing spatial reasoning, with discussions on message passing
and general mechanisms for processing irregular spatial data structures. All of these interdisciplinary elements i.e, the sensor physics, graph theory, and machine learning come together in
this chapter to create the comprehensive theoretical background that is necessary to understand
the methodological innovations and experimental contributions of the following chapters.

3

State of the Art

This chapter discusses the current research state in Warehouse automation collaborative perception, with a focus on mmWave radar-based sensing and graph neural network approaches to
environment understanding. The analysis highlights the prominent gaps between the existing
automotive perception paradigms and warehouse specific demands, which serve as the foundation for the methodological contributions of this thesis.

3.1

Warehouse Automation

Contemporary warehouse automation has transformed from basic conveyor systems to intricate
robotic setups where autonomous mobile robots (AMRs) glide through dynamic conditions.[1,
19].Compared to other robotic domains, deploying robot swarms in warehouse settings poses
special difficulties because of high operational densities, frequent obstructions from storage
infrastructure, and the requirement for constant adjustment to shifting inventory layouts.
Today’s robotic systems predominantly use individual robot perception, accomplished with a
variety of sensors, such as laser range finders, cameras, and ultrasonic sensors. [6]. However,
in crowded warehouse settings, these single-agent methods have serious drawbacks. The dependability of individual perception systems is greatly impacted by blind spots produced by tall
storage racks, dynamic occlusions from other robots and human workers, and changing lighting conditions [5]. Research on collaborative perception frameworks, in which several robots
combine their senses to create a single, cohesive representation of the environment, has been
motivated by these constraints.
The integration of collaborative behaviors in warehouse robotics extends beyond perception to
include coordinated path planning, task allocation, and swarm intelligence [20, 21]. Advances
in distributed algorithms have enabled scalable coordination, but the perception layer remains a
central bottleneck to achieving fully autonomous warehouse operations [22]. The issue is also
compounded by the real-time nature of warehouse operations, where delays in perception or
decisions propagate to cause significant losses in productivity.

3.2

Collaborative Perception Frameworks

Collaborative perception has basically developed as a transformative paradigm shift in multiagent robotics, revolutionizing how autonomous systems approach environmental understanding by using robots to systematically transcend the inherent limitations of individual sensing ca13

14

Chapter 3 - State of the Art

pabilities through information sharing protocols and advanced multi-modal fusion techniques
[23, 10]. The basic operational principle of these systems involves the systematic aggregation and integration of heterogeneous observations collected from multiple distinct viewpoints
and sensor configurations, thereby constructing a comprehensive and accurate environmental
model that surpasses the perceptual capabilities that any single autonomous agent could achieve
through independent operation. While utilising the complementary strengths of distributed
sensing networks to achieve improved spatial coverage, temporal consistency, and measurement redundancy, this collaborative approach tackles important limitations that plague individual robot perception systems, such as occlusion handling, sensor range constraints, field-of-view
restrictions, and single-point-of-failure vulnerabilities.
In earlier frameworks, collaborative perception emphasized relatively simple and well-understood
sensor fusion techniques. These took streams of raw measurement data from multiple robots of
various kinds, combined them, and processed them using probabilistic methods like Kalman filtering, particle filtering, and Bayesian inference [24]. However, these foundational approaches
faced scalability challenges and struggled with substantial bandwidth constraints, highly increasing computational complexity and communication overhead as the number of participating
agents increased within the collaborative network.
When transmitting high-dimensional raw sensor data, like point clouds, high-resolution imagery, or dense occupancy grids across multiple robots operating simultaneously, bandwidth
constraints become prohibitive, and the computational burden increases quadratically with agent
count due to pairwise communication requirements. In place of bandwidth-intensive raw sensor data streams, modern collaborative perception frameworks have systematically developed
to incorporate intelligent communication protocols, deep learning-based feature extraction algorithms, and highly efficient transmission of semantically meaningful information representations [25, 26].These approaches employ convolutional neural networks, attention mechanisms,
and graph neural networks to extract compact, yet information rich feature representations from
learned models which preserve critical perceptual information while exponentially reducing
communication requirements.
With V2X (Vehicle-to-Everything) communication protocols, the automotive industry has spearheaded the development of collaborative perception [27]. These protocols allow connected
autonomous vehicles to exchange real-time perception data, localisation information, and behavioural intentions for enhanced safety and coordination. Vehicle-to-vehicle (V2V), vehicleto-infrastructure (V2I), vehicle-to-pedestrian (V2P), and vehicle-to-network (V2N) communication are all included in V2X systems, which build entire interconnected ecosystems.
Prominent systems like Cooper, F-Cooper, and DiscoNet [28] have shown that multi-vehicle
collaboration protocols can improve detection accuracy by 15–30%. However, direct application to dense warehouse environments with high agent density, complex multi-level infrastructure, and unpredictable human-robot interactions is fundamentally problematic because automotive frameworks are optimised for outdoor environments with sparse agent distributions,
predictable motion patterns, and defined infrastructure.
Current research has identified intermediate representation sharing, where robots interchange
processed feature representations, compressed semantic maps and high-level abstractions instead of raw data streams [29, 30]. This method brings down communication overhead while
keeping critical perceptual information. The When2com framework [29] brought in attentionbased mechanisms for optimal communication timing and content selection based on uncertainty estimates and information gain metrics. V2VNet [30] utilized spatially-aware graph neu-

15

Chapter 3 - State of the Art

ral networks for message passing and feature aggregation, facilitating selective incorporation of
relevant collaborator information.
Even with technological progress, current frameworks are mainly aimed at automotive usages
and do not have adaptation mechanisms for the spatial constraints specific to warehouses, for the
way they operate at high-density, for the patterns of dynamic obstacles that they have, or for their
requirement to navigate across multiple levels. This necessitates the creation of collaborative
perception architectures specific to warehouses.

3.3

mmWave Radar for Robotic Perception

Millimeter-wave radar technology has become a significant sensing method for robotic applications and offers distinct benefits over traditional sensors in difficult conditions[31, 32]. For
industrial robotics, operating in the 60-64 GHz band, mmWave radar provides a robust performance when other methods like optical sensors or lidar struggles.
The application of millimeter wave radar in robotics has progressed from basic obstacle detection to complex mapping of environments and classifying objects within them.[33, 34]. Recent
technological advancements in radar signal processing have enabled the extraction of rich spatial information from radar returns in point cloud format similar to LiDAR data but with the
inclusion of velocity information via Doppler processing[35]. The inclusion of this extra velocity information is particularly valuable within dynamic warehouse environments where the
isolation of static infrastructure from the moving agents becomes critical.
Deep learning methods have been successfully used for processing radar data. CNN-based
models have shown great results in object detection and semantic segmentation tasks [33, 35].
However, most current research focuses on automotive applications, which have different operational needs than warehouse robotics. The high-resolution spatial data from modern MIMO
radar systems, along with their ability to perform well in various environmental conditions,
makes them suitable for collaborative perception in warehouses where traditional sensors might
have difficulties.
Integration challenges for mmWave radar in collaborative systems include aligning coordinate
frames, synchronizing time and managing sparse point cloud data. Recent research has tackled
some of these issues using calibration techniques and probabilistic data association methods
[36, 37]. However, considerable work still needs to be done to apply these solutions to the scale
and complexity of warehouse operations.

3.4

Graph Neural Networks for Spatial Reasoning

Graph Neural Networks have revolutionized the processing of non-Euclidean data structures,
offering powerful tools for reasoning about spatial relationships and dependencies [38, 39].
Graph Neural Networks (GNNs) offer an ideal foundation for encoding environmental structures perceived by robots as graphs, where the nodes denote spatial locales or entities and the
edges describe their connections.[40, 41].
The shift from primitive graph convolutional networks [42] to advanced structures such as

16

Chapter 3 - State of the Art

Graph Attention Networks (GAT) [43] and their improved versions (GATv2) [44] has given
us more diverse and capable means of dealing with the spatial aspects of a problem. GATv2 in
particular improves upon its predecessor by altering the attention mechanism it uses to be more
dynamic and lay more emphasis on the qualities of the graph in problem-solving. This leads to
using GATv2 in a more diverse set of spaces, such as in robotic scenarios.
Edge-Conditioned Convolution (ECC) networks represent another significant advancement, explicitly incorporating edge attributes into the message passing process [45, 46]. This proficiency
is especially pertinent for tasks of spatial perception in which critical information is conveyed
through geometric relationships between entities. In a warehouse, edge features can specify not
just the identity of an obstacle, but also its position relative to the robot, distances, and potential
collision risks between robots and obstacles.
In the field of robotics, GNNs have shown promise in successfully applying them to a number
of tasks, from scene understanding [47, 48] all the way to multi-agent coordination[49, 50].
Their ability to capture both local and global spatial patterns makes them a prime candidate
for working with collaborative perception, wherein information from multiple viewpoints must
be integrated in order for a coherent scene understanding to be achieved [51]. However, there
are still a lot of gaps in the application of GNNs to collaborative perception in the real world
because the majority of current work concentrates on single-agent scenarios or assumes perfect
communication.

3.5

SLAM and Mapping

Simultaneous Localization and Mapping (SLAM) in multi-agent systems has unique challenges
beyond single-agent SLAM, requiring coordination of local maps, loop closure across robots,
and handling of relative positioning uncertainty [52, 53]. The early centralized approaches of
distributed SLAM algorithms have evolved into modern decentralized systems that allow for a
large number of robots to work together [23, 54].
Current frameworks such as DOOR-SLAM [23] and Kimera-Multi [55] have shown themselves
to be quite capable of distributed SLAM, working effectively in real time and handling outlier
rejection quite well. The methods, however, are predominantly visual or LiDAR-based, with
radar still largely in the prototypical stage. The integration of multi-agent semantic information
into the SLAM process has been shown to have a very positive effect upon the data association
problem, which in turn has shown great promise for increasing computational efficiency[56].
Multi-agent SLAM faces particular difficulties because of the distinctive features of warehouse
environments such as dynamic obstacles, repetitive structures and GPS-denied operation. Perceptual aliasing in symmetric environments and preserving consistent maps when inventory
configurations change are frequent challenges for existing solutions. This brings in the need for
specialised methods that make use of collaborative tactics and the complementary advantages
of various sensing modalities.

17

3.6

Chapter 3 - State of the Art

Communication Infrastructure for Collaborative
Robotics

The effectiveness of collaborative perception depends on the communication infrastructure.
Traditional wireless technologies like Wi-Fi are not enough for the needs of robot coordination in busy warehouse environments[4]. Although 5G technology has made big improvements
in latency, bandwidth, and reliability [57], it still does not meet the ultra-reliable low-latency
communication (URLLC) requirements for safety-critical robotic applications.
Emerging 6G technologies aim to fix these limitations by offering new features like Integrated
Sensing and Communication (ISAC), which combines sensing and communication into one
framework [8, 9]. This combination is especially important for mmWave-based systems, where
the same hardware can handle both high-resolution sensing and high-bandwidth communication [11]. The chance to optimize sensing and communication resources together creates new
opportunities for effective collaborative perception in environments with limited bandwidth.
Research on communication efficient collaborative perception has found several strategies, including adaptive communication scheduling [29], learned compression of perceptual features
[30], and priority-based information sharing [58]. However, these methods have mainly been
assessed in vehicle settings, which have different spatial and temporal characteristics compared
to warehouse environments.

3.7

Research Gaps and Opportunities

Despite important progress in individual research areas, gaps still exist in creating effective
collaborative perception systems for warehouse robots. The recent collaborative perception
frameworks meant for automotive use do not meet the specific challenges found in indoor warehouse settings like higher density of agents, complex 3D structures and frequent obstructions
from storage equipment. The size and geometric complexity of warehouses need completely
different methods for data association and map representation.
Although mmWave radar presents a powerful opportunity for warehouse sensing, its incorporation into collaborative systems is barely touched upon in the literature. Most of the signal processing for current mmWave radars focuses on single platform applications and therefore does
not address the quite different problem of fusing together point clouds from several mobile
radar platforms that have different viewpoints and motion dynamics. Developing specialized
algorithms for collaborative radar perception represents a rich research opportunity.
Current GNN architectures for spatial reasoning have not been fully assessed for collaborative
robotic perception tasks. There are still questions about the best graph representations for sensor data from multiple robots. We also need to consider the trade-offs between different GNN
designs for real-time performance. Additionally, we should find ways to include temporal dynamics in graph-based spatial reasoning. The unique needs of warehouse environments, such
as the ability to differentiate between static structures and moving agents, require new methods
for building graphs and constructing features.
The integration of perception, communication and decision-making in collaborative systems

18

Chapter 3 - State of the Art

lacks unified frameworks that can optimize these areas together. Exisitng methods often view
these as separate issues. This leaves oppurtunities for optimization across different layers, which
could enhance system performance. The rise of ISAC technologies brings in a possible way
to achieve this integration. However, practical frameworks for warehouse robotics are still
underdeveloped.
These research gaps motivate the development of specialized frameworks for collaborative perception in warehouse environments, using the advantages of mmWave radar sensing and GNNs
while addressing the operational limitations of modern logistics. This thesis aims to address
these gaps through a comprehensive approach encompassing preprocessing pipelines, GNN architectures dedicated for collaborative perception, and evaluation.

4

Methodology

This chapter lays out the comprehensive framework and algorithmic fundamentals for converting sensor data into graph structures fit for collaborative perception. The methodology addresses
critical challenges in processing sparse, diverse sensor data in interconnected stages: raw data
extraction and standardisation, multi-modal synchronisation, coordinate transformation, quality improvement, data annotation, graph generation, and Graph Neural Network (GNN) design.
This chapter also introduces a multidimensional evaluation framework. This framework is designed to examine spatial reasoning abilities required for collaborative robotics.
The proposed methodology enables collaborative perception by addressing three major obstacles. First, it addresses timing differences among sensors. Second, it addresses spatial inconsistencies between robot-centric coordinate frames. Finally, it turns sparse point clouds into
organised formats suitable for machine learning.
Methodology
Framework

GNN Architectures
Data Preprocessing
Temporal
Integration

Evaluation
Attention Based
Edge Conditioned

Nodes
Edges

Graph based Collaborative
Perception Management
Layer
Figure 4.1: Methodology Framework for Collaborative Perception

19

20

4.1

Chapter 4 - Methodology

Data Preprocessing Framework

This part presents the theoretical base for transforming raw sensor data from numerous robots
into a unified, synchronized format ready for advanced processing, specifically for Graph Neural Network (GNN) application. This theoretical framework discusses raw data extraction principles, standardisation methods, synchronisation algorithms, coordinate system transformation
mathematics, data quality enhancement strategies, data annotation frameworks, and graph structure generation theory.

4.1.1

Raw Data Extraction and Standardization

Heterogeneous raw sensor data are standardized during the preprocessing stage. The data are
unified in terms of format and structure while maintaining the requisite spatial and temporal
precision necessary for sensor fusion.[59].
The Vicon motion capture system offers the high-frequency, high-accuracy ground truth positional and orientational data [60]. It creates a reference frame by analysing timestamped 6-DoF
pose information. It can manage fluctuating object counts, missing data periods, and timestamp
formats that are inconsistent. Temporal validation ensures monotonicity. Quality evaluation
entails evaluating uncertainty during static phases and validating trajectories during motion.
mmWave radar sensors create point clouds that carry spatial coordinates, signal strength, and velocity information[31]. The extraction process handles sensor msgs/PointCloud2 messages
in ROS [61], retrieving attributes such as range, SNR, and angle. High-precision timestamps
from ROS headers and databases allow for seamless synchronization.
The data extracted is converted into CSV using a standard format: coordinates in meters, time
in Unix epoch seconds, and angles in radians. Validation covers range checks, timestamp consistency and statistical outlier detection.

4.1.2

Data Synchronization

One of the difficult problems in multi-agent sensor fusion is temporal alignment. The timings
used by each sensor system varies over time. These timing discrepancies must be rectified by
the synchronisation technique while maintaining the connections within each data stream and
guaranteeing the precision required for accurate collaborative perception.[62].
From a methodological perspective, it is very important to identify and correct clock drift and
offset problems. These problems occur because each sensor system keeps its own internal clock,
leading to systematic but not uniform time differences between the sensors. While systems like
Vicon work with accurate timing, robot internal clocks can shift in relation to the Vicon system
and to one another. Delays in network communication and processing times create different
time offsets that need to be considered during synchronization.
Another challenge is posed by variations in the sampling rate. Different types of sensor systems
operate at different native frequencies and may experience variable data rates due to processing
load or communication constraints. A Vicon system, for instance, might operate at a fixed,

21

Chapter 4 - Methodology

very high frequency, while certain types of radar systems might have variable update rates. The
availability of data may also differ across types of sensor systems.
The synchronization approach adopts a multi-step process that systematically tackles each temporal alignment problem, while preserving data quality and temporal relationships. The core of
this approach is described in Algorithm 1.
Notation used in Algorithm 1:
Symbol

Description

RD1 , RD2
VD
CP
RF1 , RF2
VR
SR
SF
∆tfixed
δtthresh
t(r),t(v)
tspan (VR )
arg min
|·|

Raw radar datasets from robots 1, 2
Raw Vicon motion capture data
Configuration parameters
Temporally filtered radar data
Resampled Vicon data at fixed interval
Synchronized radar dataset
Final synchronized multi-modal dataset
Fixed resampling interval
Synchronization threshold
Timestamp functions for radar/Vicon data
Temporal bounds of Vicon data
Argument of minimum (nearest neighbor)
Absolute difference operator

Algorithm 1 Multi-Modal Data Synchronization
Require: RD1 , RD2 ,VD ,CP
Ensure: Synchronized dataset SF
Phase 1: Temporal Alignment
1: VR ← Resample(VD , ∆tfixed )
▷ Fixed interval resampling
2: RF1 , RF2 ← Filter(RD1 , RD2 ,tspan (VR ))
▷ Temporal bounds
Phase 2: Cross-Robot Synchronization
3: SR ← 0/
4: for ri ∈ RF1 do
5:
r j ← arg minr∈RF2 |t(ri ) − t(r)|
▷ Nearest neighbor
6:
if |t(ri ) − t(r j )| ≤ δtthresh then
7:
SR ← SR ∪ {(ri , r j )}
8:
end if
9: end for
Phase 3: Vicon-Radar Alignment
10: for (ri , r j ) ∈ SR do
11:
vk ← arg minv∈VR |t(ri ) − t(v)|
12:
SF ← SF ∪ {(ri , r j , vk )}
13: end for
14: return SF = {(r1 , r2 , v) : |t(r1 ) − t(r2 )| ≤ δt, |t(r1 ) − t(v)| ≤ δt}

The establishment of reference time starts with identifying the best available timing source
among all sensing systems. In this instance, the Vicon system functions as the temporal reference, marking the master timeline TV .

22

Chapter 4 - Methodology

Vicon data resampling deals with the high and variable sampling frequency of motion capture
data by resampling it to a consistent temporal grid {ti }N
i=1 . This process uses interpolation
techniques suitable for position p(t) and orientation q(t) data, such as forward-fill and linear
interpolation. These techniques help maintain smooth trajectories while ensuring consistent
temporal spacing. The choice of resampling frequency is a balance between time resolution
and computational efficiency.
Radar data temporal filtering limits radar measurements to Vicon temporal bounds through
RF1 , RF2 = {r ∈ RD1 , RD2 | t(r) ∈ [tmin (VR ),tmax (VR )]}, ensuring all radar data has its corresponding motion capture information. Cross-robot radar synchronization aligns multiple radar
streams by matching timestamp correspondence within tolerance δtthresh , where |t(ri ) −t(r j )| ≤
δtthresh , typically through nearest-neighbor temporal matching.
Vicon-radar temporal alignment synchronizes radar dataset SR with Vicon timeline TV using
s f = (sr , v), where v = arg minv′ ∈VR |t(sr )−t(v′ )|, accounting for systematic time offsets ∆tsys between clocks. Movement detection identifies active motion periods by using velocity thresholds
v(t) = ∥ dp(t)
dt ∥, with motion detected when v(t) > vth . Activity segmentation divides sessions
into distinct phases defined as Segment = {t | v(t) > vth and other criteria}, based on sustained
motion, proximity or task execution phases.

4.1.3

Coordinate Transformation

To effectively fuse sensors across multiple platforms, the colloborative perception systems must
transform sensor measurements taken in different local coordinate frames into a single unified
global reference system. This is done through the use of hierarchical reference frames, temporal
synchronization and geometric relationships between sensors, robots and the global system.
The hierarchy of coordinate frames starts with the Global Reference Frame, given by the Vicon
motion capture system, which serves as the spatial authority with a fixed origin and laboratoryaligned axes. Robot Body Frames are set for each robotic platform and are usually centered
at the robot’s geometric center, following standard robotics guidelines (X-forward, Y-left, Zup). These frames move as robots navigate, with poses tracked continuously by motion capture.
Sensor Local Frames are individual coordinate systems for the sensing devices attached to
robots. For radar sensors, the origin is at the sensor’s phase center, and the axes align with the
mounting setup and beam direction. The connection to robot body frames stays fixed for rigidly
mounted sensors, requiring careful calibration.
The systematic transformation chain moves from the Sensor’s Local Frame to the Robot Body
Frame to the Global Reference Frame, compensating for sensor mounting offsets and orientations, for the dynamic body poses from motion capture, and temporal synchronization between
measurements and pose estimates. The mathematical foundation changes a point Plocal in sensor
coordinates to Pglobal in the global frame. It does this through a series of rigid body transformations that account for both rotation and translation across different reference frames.
Pglobal = Rrobot · (Rsensor · Plocal + Tsensor ) + Trobot

(4.1.1)

where Pglobal denotes the point coordinates in the global reference frame, Plocal is the original
point coordinates in the sensor local frame, Rrobot and Trobot expresses the robot’s orientation

23

Chapter 4 - Methodology

(3×3 rotation matrix) and position (3×1 translation vector) obtained from the motion capture
system and Rsensor and Tsensor represent the sensor’s orientation and position relative to the
robot body frame, obtained by means of calibrating processes [63].
For collaborative perception systems that use rigidly mounted sensors, where the rotational
offset Rsensor can be pre-calibrated and integrated into the sensor measurements or treated as
identity for sensors aligned with the robot body frame, the transformation equation simplifies
to:
Pglobal = Rrobot · (Plocal + Tsensor ) + Trobot

(4.1.2)

This simpler form minimizes computing requirements while keeping transformation accuracy
for systems with well-defined sensor mounting arrangements.
Computation of the rotation matrix requires converting orientation data from motion capture
systems that is normally given in either Euler angles, axis-angle representations, or quaternion
formats into proper rotation matrices suitable for coordinate transformations. Careful attention
must be given to rotation order rules, such as ZYX and XYZ and sign rules. This is important
for maintaining consistency throughout the transformation process. For robotic systems that
primarily operate in planar environments, the transformation can be further simplified to 2D
rotations that involve only the yaw angle:
ò
cos(θ ) − sin(θ )
R2D =
sin(θ ) cos(θ )
ï

(4.1.3)

where θ is the yaw angle of the robot platform relative to the global reference frame. This
2D formulation greatly reduces the computational complexity for ground-based robotic applications. It keeps the spatial accuracy needed for successful collaborative perception tasks.

4.1.4

Data Filtering and Quality Enhancement

Real-world radar data contain more noises and many artifacts that must be dealt with to achieve
a reasonable level of reliable performance for machine learning. Radar-specific noise sources
include thermal noise from receiver electronics, environmental clutter from static reflectors
like walls and furniture, multi-path reflections which creates ghost detections from indirect
signal paths and side-lobe contamination leading to angular measurement errors. Additional
challenges are from range and Doppler ambiguities, electromagnetic interference and motioninduced artifacts. These issues can highly reduce data quality if not handled properly.
A filtering pipeline tackles these challenges with the help of a series of specialized techniques.
Operational Arena Filtering applies spatial bounding box limits based on known environment
geometry to remove clearly incorrect measurements. Signal-to-Noise Ratio (SNR) Filtering
eliminates detections below adjustable thresholds, typically between 6 and 15 dB, using either
fixed or adaptive methods that depend on local noise analysis. Statistical Outlier Detection
uses k-nearest neighbor analysis to find measurements that do not match their surrounding
points. Points are marked as outliers when their average neighbor distance is more than a
certain number of standard deviations above global statistics. Height-Based Filtering removes

24

Chapter 4 - Methodology

measurements outside relevant vertical ranges to get rid of ground reflections and ceiling artifacts. Field-of-View (FoV) Filtering limits detections based on documented sensor specifications, considering azimuth and elevation constraints and platform orientation for mobile
systems. Temporal Consistency Filtering checks the persistence of measurements across consecutive frames to eliminate random false detections and includes motion checks to confirm
reasonable velocity patterns. Implementation aspects include the best order for filters to ensure efficiency, memory-efficient chunked processing for large datasets, and careful parameter
tuning through cross-validation to find the right balance between noise reduction and signal
preservation.

4.1.5

Data Labeling and Ground Truth Generation

Generating accurate ground truth labels for supervised learning tasks like binary occupancy
prediction is necessary. This stage outlines methods of using knowledge about the environment,
geometric models, and robot positioning to automatically generate accurate annotations.
The classification of objects in the annotation framework falls into four main categories: specific
workstations (AS 1, AS 3, AS 4, AS 5, AS 6), robots, arena boundaries, and unknown objects.
Ground truth generation uses known reference positions for workstations, robot locations from
Vicon tracking, and predefined arena boundary coordinates. The method combines these static
geometric models with real-time position data. It processes each timestamp independently,
without relying on previous data.
The detection of workstations employs geometric models using predefined dimensions (1.0m
× 0.65m) and configuration file positions. The method focuses on radar edge detection features
through specific proximity testing with a tolerance of 20 cm. Point association uses first-match
sequential testing for each workstation. Successful proximity matches decide the label assignment.
Current-frame Vicon positioning is used in the detection of robots and with it rectangular models
(32cm × 24cm) can be created that have a tolerance parameter of 12cm. Robot coordination
prevents self-detection but keeps the ability to detect other robots with the help of geometric
boundary testing. The system works with two robots at the same time and does not rely on
motion prediction or temporal smoothing.
Boundary annotation models define arena limits as rectangular regions (x: -9.1 to 10.2m, y:
-4.42 to 5.5m) with different tolerance settings: 25cm for straight edges and 35cm for corners.
The classification involves direction and corner identification to understand complex radar reflection patterns.
The processing architecture implements a classification of points: proximity to occupied points
and then points which are still unknown. Dataset configuration management extracts workstation positions from Excel files. It uses pattern matching with backup methods for temporal
association. Output generation produces two annotation columns, one for specific identifiers
and another for general categorical labels.

25

4.1.6

Chapter 4 - Methodology

Graph Structure Generation

Graph Neural Networks relies on the data structured as mathematical graphs with nodes, edges,
and associated feature vectors. In this stage, we convert annotated point cloud data from the
collaborative robots into graph representations that retain the spatial relationships of the points
while incorporating the sensor information and temporal dynamics.
A collaborative perception graph G is defined as:
G = (V, E, X, A)

(4.1.4)

where V = {v1 , v2 , ..., vn } is the set of nodes corresponding to spatial voxels having aggregated
sensor data from both robots. The edge set E ⊆ V × V gives the spatial relationships between
neighboring nodes, while the node feature matrix X ∈ Rn×d has d-dimensional feature vectors
capturing spatial, collaborative and temporal information. The adjacency matrix A ∈ {0, 1}n×n
encodes graph connectivity based on spatial proximity relationships. Graph construction processes use pre-annotated point cloud data from both collaborative robots. They gather pointlevel annotations during voxelization to create node-level classification information.
The voxelization procedure transforms the unstructured point cloud data from the two collaborative robots into a regular, unified 3D grid format. This format is necessary for subsequent
processing with graph neural networks. Each radar point p = [x, y, z] from either robot is mapped
to voxel indices. This is done based on the chosen voxel resolution δ by using floor operations:
voxel x = ⌊x/δ ⌋,

voxel y = ⌊y/δ ⌋,

voxel z = ⌊z/δ ⌋

(4.1.5)

The aggregation process usually combines the valid radar points from both robots in the same
voxel structure. It keeps the individual contributions of each robot and creates a unified spatial
representation. Label aggregation works at the voxel level by using majority voting among the
pre-annotated points in each voxel.
label(vi ) = arg max |{p ∈ vi : label(p) = c}|
c∈Classes

(4.1.6)

Each node vi is associated with a 17-dimensional feature vector capturing spatial and collaborative information:
xi = [sspatial
, scollaborative
]T
(4.1.7)
i
i
The spatial feature component sspatial
∈ R13 includes several spatial representations. These
i
consist of normalized position coordinates for scale-invariant spatial encoding, raw position
coordinates for absolute spatial locations, position relative to the spatial center for local context,
position relative to the coordinate system origin for global reference, and distance to the spatial
center as a measure of centrality. Collaborative features scollaborative
∈ R3 represent the unique
i
aspect of this work. They quantify the contributions of individual robots within each spatial
voxel. This includes features that count the number of radar points contributed by each robot and
a collaboration score that measures spatial overlap between robots, calculated as the minimum
contribution ratio of each robot within each voxel.

26

Chapter 4 - Methodology

Point Clouds

Voxelization

Node Features

Edge Creation

Pytorch Geometric

Figure 4.2: PCL to Graph Conversion Pipeline.

The adjacency matrix A uses a completely connected graph structure to capture spatial relationships between all the voxels in each frame. For each node vi , connections are made to all other
nodes:
®
1 if i ̸= j
Ai j =
(4.1.8)
0 if i = j
This connectivity in its most complete form allows the preservation of spatial relationships between all pairs of voxels during processing by a graph neural network, ensuring comprehensive
collaborative perception modeling across the entire spatial domain.
The temporal modeling of collaborative perception is done with the help of a sliding window
(w)
that incorporates past and current time steps. The causal temporal window Wt for a window
size w is given as:
(w)
Wt = {Ft−w+1 , Ft−w+2 , . . . , Ft−1 , Ft }
(4.1.9)
In temporal window models, nodes get temporal offset features that tell their causal position
in the historical sequence; they get relative indices, with negative values indicating historical
frames and zero representing the current time step.
Notation used in Algorithm 2:
Symbol

Description

G = (V, E, X, A)
Prt
p ∈ R3
δ
vid = ⌊p/δ ⌋
V
E
X ∈ R|V |×d
A ∈ {0, 1}|V |×|V |
xi ∈ R17
sspatial ∈ R13
cr1, cr2
scollab = min(cr1, cr2)/cr1 + cr2
to f f set
y ∈ {0, 1}|V |
(w)
Wt

Collaborative perception graph
Radar point cloud from robot r at time t
Individual radar point coordinates
Voxel resolution parameter
Voxel identifier from point coordinates
Set of spatial voxel nodes, |V | ∈ [2, 500]
Set of edges, |E| = |V |(|V | − 1)/2
Node feature matrix
Adjacency matrix
Feature vector for node i
Spatial feature components
Point counts from robots 1, 2
Collaboration score
Temporal sequence position
Binary occupancy labels
Temporal window of size w

27

Chapter 4 - Methodology

Algorithm 2 Point Cloud to Graph Conversion
Require: Point clouds P1t , P2t , voxel resolution δ
Ensure: Graph G = (V, E, X, A) with labels y
Phase 1: Spatial Voxelization
1: Vmap ← 0/
2: for r ∈ {1, 2}, p ∈ Prt do
3:
vid = ⌊px /δ ⌋, ⌊py /δ ⌋, ⌊pz /δ ⌋
4:
Vmap [vid ] ← Vmap [vid ] ∪ {(p, r, label(p))}
5: end for
Phase 2: Node Feature Construction
6: for vi ∈ V do
(spatial)
← [pnorm , praw , prel , porigin , dcenter ]
7:
si
8:
cr1 , cr2 ← point counts from robots 1, 2
r1 ,cr2 )
9:
scollab = min(c
cr1 +cr2 if cr1 + cr2 > 1, else 0

▷ 13D

(spatial)

, cr1 , cr2 , scollab ]T ∈ R16
xi ← [si
yi ← majority vote({label(p) : (p, r, l) ∈ vi })
end for
Phase 3: Graph Topology
13: Ai j = 1 if i ̸= j, else 0
14: E = {(i, j) : Ai j = 1}, |E| = |V |(|V | − 1)
15: return G = (V, E, X, A) with y ∈ {0, 1, 2, 3, 4}|V |
10:
11:
12:

▷ Initialize voxel mapping

▷ Complete connectivity

Collaborative graph generation has computational complexity that scales with the total number
of points from both robots during the voxelization process(O(|P1 |+|P2 |)). It also scales quadratically with the number of resulting voxels during full connectivity construction (O(|V |2 )). The
space complexity involves storing 17-dimensional node features (O(|V | × 17)) and dense edge
representations (O(|V |2 )).
A strong data segmentation strategy is essential for developing and assessing models. Preserving Temporal Integrity is vital for time-series datasets. Partitioning must be done at the level
of experimental sessions or sequences to avoid data leakage, which can occur when temporally sequential measurements are divided between different splits (e.g., training and testing).
The concept of Experimental Diversity Distribution seeks to ensure that every partition (train,
validation, test) includes representative samples of varied experimental conditions, behaviors,
and setups to aid model generalization. The Statistical Balance Requirements aim to maintain
uniform statistical attributes across partitions, such as balanced class distribution and comprehensive spatial/temporal coverage, to guarantee an equitable evaluation.

4.2

Graph Neural Network

This section presents the detailed theoretical foundations of the Graph Neural Network architectures that are suitable for collaborative perception tasks, detailing the specific architectures
used in this research. The theoretical framework includes attention-based mechanisms, edgeconditioned convolutions and design principles for spatial reasoning.

28

4.2.1

Chapter 4 - Methodology

Graph Feature Representation

The theoretical foundations of graph feature representation for collaborative perception consist
of node and edge design principles that encode spatial and temporal information within graph
structures. Node features link spatial coordinates with voxel qualities to achieve precise localisation. They include various information for context, collaborative context via relative positions
among multiple robots, and temporal dependencies for dynamic modelling. These geometric
grounds enable coordinated interpretation in shared environments.
Relational information is captured by edge features through geometric relationships, encodings
of distance and angle, and patterns of connectivity. Systematic edge formation, like that in
k-nearest neighbor and radius-based approaches, is based on proximity criteria and theoretical
frameworks of edge formation. To adapt to changing relationships in both spatial and temporal contexts and to edge formation in theoretical frameworks, dynamic edge weights are used.
These weights reflect mathematically modeled relationships that consider both the relevant geometric constraints and the temporal evolution of the features.
Three fundamental paradigms, which offer complementary takes on spatial reasoning, form the
basis of collaborative perception GNN architectures. The first paradigm is attention-based architectures. The neighbors are weighted dynamically and through learned mechanisms that establish a framework in which the neighboring information can be focused on. Edge-conditioned
architectures include spatial relationships using edge-specific transformations. These transformations adjust computations. Temporal integration frameworks model changing relationships
over time and how spatial configurations evolve. Together, these approaches effectively handle
complex spatial and temporal dynamics in collaborative perception situations.
These theoretical foundations guide the selection and implementation of specific GNN architectures for collaborative perception, as detailed in the following sections.

4.2.2

Architectural Selection Rationale

The selection of specific Graph Neural Network architectures for collaborative perception requires balancing computational efficiency, expressiveness, and spatial relationship modeling
capabilities in dynamic warehouse environments.
Attention mechanisms, originally designed for tasks involving sequence-structured data (e.g.,
machine translation [64]), has now been successfully applied to graph-structured data. The
main idea behind the mechanism is to allow each node to weight the importance of its neighbors
differently, in the process of information aggregation, rather than treating all neighbors equally.
This adaptive weighting provides a more subtle and powerful approach to capturing relevant
local context.
Graph Attention Network v2 (GATv2) was selected over the original GAT architecture due to
fundamental improvements in attention mechanism expressiveness. Standard GAT computes
static attention where ranking is unconditioned on the query node, limiting its ability to express
simple graph problems. GATv2 implements
dynamic attention through a modified scoring func
tion: ei, j = a⊤ LeakyReLU W[hi ∥ h j ] , enabling every node to attend to any other node[44].

Published as a conference paper at ICLR 2022

29

Chapter 4 - Methodology

k0 k1 k2 k3 k4 k5 k6 k7 k8 k9
q0
q1
q2
q3
q4
q5
q6
q7
q8
q9
0.4
0.3
0.2
0.1

k0 k1 k2 k3 k4 k5 k6 k7 k8 k9
q0
q1
q2
q3
q4
q5
q6
q7
q8
q9
1.0

0.08 0.10 0.10 0.07 0.08 0.08 0.11 0.09 0.20 0.08
0.05 0.10 0.10 0.04 0.04 0.04 0.13 0.06 0.38 0.04
0.05 0.10 0.10 0.04 0.05 0.05 0.13 0.06 0.38 0.05
0.08 0.10 0.10 0.07 0.08 0.08 0.10 0.09 0.24 0.08
0.08 0.09 0.09 0.07 0.07 0.07 0.10 0.08 0.27 0.07
0.09 0.11 0.11 0.08 0.09 0.08 0.11 0.10 0.16 0.09
0.04 0.10 0.11 0.03 0.04 0.04 0.14 0.06 0.40 0.04
0.07 0.09 0.09 0.06 0.07 0.07 0.10 0.08 0.29 0.07
0.04 0.11 0.11 0.02 0.04 0.03 0.14 0.07 0.41 0.04
0.07 0.09 0.09 0.06 0.07 0.07 0.11 0.08 0.30 0.07

q0
q1
q2
q3
q4
q5
q6
q7
q8
q9

0.95 0.00 0.00 0.01 0.01 0.00 0.00 0.02 0.01 0.00
0.01 0.92 0.01 0.01 0.01 0.00 0.01 0.01 0.00 0.02
0.00 0.00 0.95 0.00 0.00 0.01 0.02 0.01 0.00 0.00
0.01 0.01 0.00 0.94 0.00 0.01 0.00 0.00 0.02 0.01
0.00 0.00 0.00 0.00 0.96 0.00 0.00 0.01 0.01 0.00
0.00 0.01 0.01 0.01 0.01 0.89 0.01 0.01 0.04 0.02
0.00 0.01 0.04 0.00 0.01 0.01 0.86 0.02 0.01 0.03
0.04 0.02 0.01 0.01 0.03 0.01 0.00 0.87 0.00 0.01
0.01 0.00 0.01 0.01 0.01 0.01 0.01 0.00 0.94 0.00
0.01 0.02 0.01 0.01 0.01 0.01 0.01 0.00 0.00 0.93

0.8

q0
q1
q2
q3
q4
q5
q6
q7
q8
q9

0.6
0.4
0.2
0.0

k0 k1 k2 k3 k4 k5 k6 k7 k8 k9
(a) Attention in standard GAT (Veličković et al. (2018))

k0 k1 k2 k3 k4 k5 k6 k7 k8 k9

(b) Attention in GATv2, our fixed version of GAT

Figure 4.3: Attention mechanisms comparison: (a) Standard GAT with static attention, (b)
Figure 1:with
In adynamic
completeattention
bipartite graph
GATv2
[44]. of “query nodes” {q0, ..., q9} and “key nodes” {k0, ..., k9}:
standard GAT (Figure 1a) computes static attention – the ranking of attention coefficients is global
for all nodes in the graph, and is unconditioned on the query node. For example, all queries (q0 to
q9) attend mostly to the 8th key (k8). In contrast, GATv2 (Figure 1b) can actually compute dynamic
The
fundamental difference between the standard GAT and GATv2 is depicted in Figure 4.3.
attention, where every query has a different ranking of attention coefficients of the keys.

Within the experimental context of a complete bipartite graph, the standard GAT (Figure 4.3a)
displays static attention characteristics, where all query nodes show nearly uniform attention
patterns towards a common group of key nodes. This constraint occurs because the attention
Veličković
et al. also
generalizes
Transformer’s
(Vaswani
et al., of
2017)
ranking
remains
global
and isn’tthe
tailored
to the unique
features
eachself-attention
query node.mechanism,
Conversely,
from
sequences
to
graphs
(Joshi,
2020).
GATv2 (Figure 4.3b) supports dynamic attention, allowing each query node to generate its own
Nowadays,
GATofis attention
one of the
most popular
architectures
(Bronstein
et al., 2021) behavior
and is
distinct
ranking
coefficients
for GNN
the shared
key nodes.
This query-centric
considered
as
the
state-of-the-art
neural
architecture
for
learning
with
graphs
(Wang
et
al.,
2019a).
permits spatial locations within collaborative perception scenarios to dynamically concentrate
Nevertheless,
in this paperareas
we show
that GAT
actually spatial
compute
the expressive,
well known,
on
relevant neighboring
according
to does
their not
individual
context
and specific
collabotype of attention (Bahdanau et al., 2014), which we call dynamic attention. Instead, we show that
rative
needs.
GAT computes only a restricted “static” form of attention: for any query node, the attention function
is monotonic with respect to the neighbor (key) scores. That is, the ranking (the argsort) of attention
coefficients is shared across all nodes in theT graph, and is unconditionedon the query node. This fact
exp a LeakyReLU W[hi ∥ h j ]
severely hurts the expressiveness
(4.2.1)
αi j = of GAT, and is demonstrated in Figure 1a.

∑

exp (aT LeakyReLU (W[hi ∥ hk ]))

(i)
Supposedly, the conceptual ideak∈N
of attention
as the form of interaction between GNN nodes is
orthogonal to the specific choice of attention function. However, Veličković et al.’s original design of
where
αi spread
weight
that(Wang
node eti assigns
to Yang
information
fromWang
nodeet j,al.,hi2019c;
and h j
j is thetoattention
GAT has
a variety of
domains
al., 2019a;
et al., 2020;
are
the
feature
vectors
of
nodes
i
and
j
respectively,
W
is
a
learnable
shared
weight
matrix,
Huang and Carley, 2019; Ma et al., 2020; Kosaraju et al., 2019; Nathani et al., 2019; Wu et al., 2020;
Zhang et al., 2020)
and has become
the default
implementation
of “graph
attention
network”
in all
LeakyReLU
is the non-linear
activation
function,
a is a learnable
attention
vector,
N (i) denotes
popular
GNN
libraries
such
as
PyTorch
Geometric
(Fey
and
Lenssen,
2019),
DGL
(Wang
et
al.,
the neighborhood of node i, and ∥ represents concatenation.
2019b), and others (Dwivedi et al., 2020; Gordić, 2020; Brockschmidt, 2020).

The
mechanism
of multi-head
attention
expands
this ideaa by
performing
multiplefunction
indepenTo overcome
the limitation
we identified
in GAT,
we introduce
simple
fix to its attention
by only modifying the order of internal operations. The result is GATv2 – a graph attention variant
2

30

Chapter 4 - Methodology

dent functions of attention in parallel. This allows the model to capture all kinds of spatial
relationships at once. Each attention head can specialize in different aspects. This includes
proximity-based attention for local neighborhood aggregation, for object-level reasoning, longrange attention for global context and directional attention for oriented spatial relationships.
The multi-head formulation is given by:
!
h′i = ConcatK
k=1 σ

∑

αikj Wk h j

(4.2.2)

j∈N (i)

where K is the number of independent attention heads, αikj is the attention weight for the k-th
head and Wk is the weight matrix for the k-th head.
Algorithm 3 Attention Computation for a Single GATv2 Head k
Require: Node features for central node i (hi ) and neighbor j (h j ); Weight matrix Wk ; Attention vector aTk .
Ensure: Aggregated features for node i from head k, denoted h′′i .
1: for all node x in the graph do
2:
Transform node features: h′x ← Wk hx
3: end for
4: for all node i do
5:
Initialize: h′′i ← 0
6:
Compute attention logits: ei j ← aTk LeakyReLU([h′i ∥ h′j ]) for j ∈ N (i)
7:
8:
9:
10:

exp(ei j )
∑l∈N (i) exp(eil )
h′′i ← ∑ j∈N (i) αi j h′j

Normalize: αi j ←
Aggregate:
end for
return h′′i

The final node representation combines data from all attention heads using various composition
methods. Concatenation retains all attention-specific information while increasing dimensionality. In contrast, averaging reduces dimensionality while preserving diversity from various
attention perspectives. Alternatively, learned combination approaches such as attention layers
over head outputs or weighted sums with learnable weights can be utilised.
Effective GATv2 architectures brings in several key design principles. Normalization strategies
usually take a two-part approach. At first, they use Batch Normalization after linear transformations. This helps stabilize activations before computing attention. Second, they apply Layer
Normalization after aggregating attention. This step manages different neighborhood sizes and
feature scales. Skip connections are important for training deeper models. They help prevent
the problem of gradient vanishing. These connections are often set up as residual connections:
(l+1)
(l)
(l)
(l)
hi
= hi + GATv2Layer(hi , {h j } j∈N (i) ). Some advanced versions might include gated
skip connections or attention-weighted skip connections.
Regularisation strategies prevent overfitting in a variety of ways. Dropout is often used with
attention coefficients, hidden representations following GNN layers, and input features. DropEdge removes edges randomly during training to reduce the co-adaptation of neighbouring nodes.
Attention dropout focusses on attention weights, αi j . When combined with adequate normalisation, these regularisation methods allow training of deep GATv2 architectures while preserving

31

Chapter 4 - Methodology

the model’s capacity to grasp complicated spatial relationships in collaborative perception settings.
Edge-Conditioned Convolution (ECC) was selected for its capacity to incorporate spatial relationships into the message-passing process. By conditioning the convolution operation on
pairwise geometric information, ECC can better capture distinctions such as direct adjacency
or overlapping coverage crucial for accurate occupancy prediction in structured warehouse environments.
The fundamental idea behind ECC is that spatial relationships between nodes can be modeled
through adaptive transformations during message passing. This enables context-specific modifications to node features before they are aggregated. The update rule for a node h′i can be
expressed as:

h′i = Act

1
|N (i)|

!

∑

FΘ (hi , h j )h j + b

(4.2.3)

j∈N (i)

where FΘ (hi , h j ) is a neural network that generates adaptive weights based on node feature
combinations, b is a learnable bias term, |N (i)| is the cardinality of the neighborhood for
normalization, and Act is a non-linear activation function.
The conditioning network FΘ processes concatenated node features through several fully connected layers:
FΘ (hi , h j ) = WL · ReLU(WL−1 · ReLU(...W1 · [hi ||h j ] + b1 )... + bL−1 ) + bL

(4.2.4)

This design allows ECC to learn complex, nonlinear transformations based on spatial relationships, capturing geometric patterns that fixed aggregation schemes might miss. For collaborative perception tasks, the conditioning network learns to distinguish between different types of
spatial configurations through node feature interactions.
The conditioning network computes adaptive weights based on node feature combinations, accepting concatenated node features as input through fully connected layers with ReLU activations and dropout regularization. The output generates parameters that define the adaptive
transformation matrix Wi j , allowing the network to modify its computational behavior based
on specific node pair characteristics.
ECC naturally supports permutation equivariance through its symmetric treatment of node pairs
and adaptive weight generation. The architecture learns spatial relationships implicitly through
node feature interaction patterns, enabling effective modeling of collaborative perception scenarios while requiring careful memory management for scalable implementation.
GATv2 and ECC operate within the message passing framework [46], updating node states

32

Chapter 4 - Methodology

through:
Ä (l) (l) ä
(l+1)
mi← j = ψ hi , h j

(4.2.5)

(l+1)

(4.2.6)

(l+1)

mi

=

M

mi← j

j∈N (i)
(l+1)

hi
where ψ is the message function,

L

Ä (l) (l+1) ä
= φ hi , mi

(4.2.7)

is aggregation, and φ is the update function.
(l)

GATv2 uses the message function αikj Wk h j with learned attention weights, weighted sum ag(l)

(l)

(l)

gregation and multi-head concatenation. ECC uses FΘ (hi , h j )h j with transformations based
on node feature combinations, mean/sum aggregation and activation with bias. GATv2 excels
at dynamic neighbor weighting through attention mechanisms. In contrast, ECC specializes in
adaptive transformations based on node interactions, enabling context-specific message passing
without requiring explicit edge attributes.
While GraphSAGE offers computational advantages through neighbor sampling [65], its sampling mechanism can be counterproductive for collaborative perception applications. GraphSAGE samples only neighbor subsets rather than considering complete spatial relationships,
potentially losing important spatial context critical for precise occupancy reasoning. Standard
GCNs treat all neighbors equally through uniform aggregation [42], limiting their ability to
model heterogeneous spatial relationships in collaborative perception. The attention mechanisms in GATv2 and edge-conditioning in ECC provide more sophisticated approaches to handle spatial heterogeneities and collaborative coordination requirements.
The selection of both GATv2 and ECC provides complementary benefits: GATv2 excels at
adaptive importance weighting among spatial regions, while ECC provides superior geometric
relationship modeling. This architectural diversity strengthens experimental validity by ensuring conclusions about collaborative perception effectiveness are not dependent on single GNN
variant characteristics.

4.2.3

Temporal Integration

The temporal integration of time-series information into graph representations occurs through
causal windowing and motion-aware features. Short windows ([t −2,t −1,t]) capture immediate motion patterns, while extended windows ([t −4, . . . ,t]) model long-term trajectories with
temporal consistency.
Temporal encoding use causal indices (toffset ∈ [−N, . . . , 0]) for time ordering. It also includes
motion synchronisation techniques for relative dynamics. Collaborative motion estimation
utilises finite differences:

33

Chapter 4 - Methodology

(t−1)

(t)

(t)
vr,i
(t)

≈

ar,i ≈

pr,i − pr,i

,
∆t
(t−2)
(t−1)
(t)
pr,i − 2pr,i + pr,i
∆t 2

(4.2.8)
,

(4.2.9)

where r ∈ {1, 2} indicates robot and i indexes voxels. Inter-robot features measure the time
overlap for better coordination.
Robot fusion uses early fusion to merge sequences before GNN processing. It also employs
late fusion, where each robot processes independently with cross-robot attention. Additionally,
interleaved fusion alternates between robot-specific and shared modules.
Causal attention maintains real-time compatibility by restricting access to future time steps:
Ç
Attention(Q, K,V ) = softmax

å
QK ⊤
√ + Mcausal V,
dk

where Mcausal blocks future timestamps. Temporal networks create directed structures that are
optimized for effective multi-frame processing by connecting nodes solely to previous frames.

4.2.4

Class Imbalance Handling

Collaborative perception in warehouse environments suffers from a severe class imbalance because the occupancy patterns are spatially sparse. The vast majority of the operational space is
unoccupied, with concentrated activity around workstations, boundaries, and robot pathways.
This distribution favors the majority classes and lowers sensitivity to detecting minority classes.
As a result, models will reach high overall accuracy by just predicting the majority class, but
they will struggle to learn important characteristics of minority classes.
By assigning different weights to minority and majority classes during training, weighted loss
functions address this issue. To solve distributional imbalance, the weighted Binary CrossEntropy loss modifies the conventional formula:

Lweighted = −

1 N
∑ [w pos · yi · log(σ (ẑi)) + wneg · (1 − yi) · log(1 − σ (ẑi))]
N i=1

(4.2.10)

where ẑi represents raw logits, yi denotes ground truth binary labels, σ is the sigmoid function,
and w pos , wneg are class weights. The positive weight is calculated using inverse class frequency:

w pos =

Nunoccupied
Noccupied

(4.2.11)

This weighting makes sure that minority classes get proper attention during backpropagation. It
helps prevent too much bias toward the majority class while keeping overall accuracy intact. The
implementation works smoothly with PyTorch Geometric by using BCEWithLogitsLoss with

34

Chapter 4 - Methodology

determined positive weights. This approach makes sure the stability in numbers and efficiency
in computation. It maintains the ability for collaborative learning while creating balanced sensitivity to both occupied and unoccupied areas. This balance is neccessary for accurate navigation
and obstacle avoidance in changing warehouse environments.

4.3

Evaluation Framework for Collaborative Perception

This study carries out an in-depth evaluation of occupancy prediction using GNNs in collaborative environments. It uses a traditional metrics-based approach yet complements it with a
unique spatial reasoning perspective to offer a more nuanced performance analysis across six
GNN architectures. The framework meets the needs of collaborative robotics. It shows how
good occupancy prediction affects navigation safety, coordination efficiency and operational
reliability. This is identified through several analytical views including classification performance, confusion matrix analysis, spatial accuracy evaluation and distance-based assessment.

4.3.1

Confusion Matrix Analysis

Confusion matrix analysis allows for a detailed evaluation of discrimination across different
architectural families. It highlights behavior relevant to collaborative robotics. The framework
looks at the following components:
Table 4.1: Confusion Matrix Terminology for Occupancy Prediction
Metric
True Positives (TP)
True Negatives (TN)
False Positives (FP)
False Negatives (FN)

Description
Occupied regions correctly detected as occupied. Crucial
for reliable obstacle detection and collision avoidance.
Free space correctly identified as free. Enables safe and
efficient navigation in dynamic environments.
Free space incorrectly classified as occupied. Can lead to
unnecessary avoidance maneuvers and reduced efficiency.
Occupied regions incorrectly classified as free. Poses safety
risks by allowing potential collisions with undetected obstacles.

The confusion matrix is defined as:
ï

T N FP
FN T P

ò

Safety-critical evaluation focuses mainly on reducing False Negatives (FN) to make sure all
occupied areas are detected for effective collision avoidance. The framework also tries to keep
a balanced trade-off between sensitivity and the False Positive rate, by looking at discrimination
trends across different GNN variants and classifying deployment readiness based on safety and
operational efficiency.

35

4.3.2

Chapter 4 - Methodology

Classification Evaluation Metrics

The classification evaluation uses traditional metrics to allow for standardized architectural
comparison and to evaluate the suitability of models for use in resource-limited situations. The
main metrics include:
Table 4.2: Evaluation Metrics for Voxel-wise Classification
Metric

Formula

Accuracy (Acc)

Acc =

Description

TP+TN
T P + T N + FP + FN

Proportion of correctly classified voxels
(both occupied and free) out of all predictions.

Precision (P)

P=

TP
T P + FP

Indicates the fraction of predicted occupied voxels that are truly occupied.

Recall (R)

R=

TP
T P + FN

Measures the ability to detect actual occupied voxels.

F1-score (F1 )

F1 = 2 ×

P×R
P+R

Harmonic mean of precision and recall,
balancing both false positives and false
negatives.

These metrics give a clear understanding of how the model performs. They balance prediction
accuracy and computational cost. This balance is neccessary for using the model in real-world
robotics and spatial perception tasks.

4.3.3

Spatial Accuracy Evaluation

Evaluating the spatial accuracy of a collaborative robotic system requires more than just traditional classification methods to assess spatial reasoning capabilities. Distance-based assessment
addresses robotics-specific spatial accuracy requirements through:

Distance Accuracy(τ) =

|{p ∈ Ppred : ming∈Gtruth ||p − g||2 ≤ τ}|
|Ppred |

(4.3.1)

where Ppred is the predicted occcupied points, Gtruth is the ground truth occupied points, τ
represents distance tolerance and ||p − g||2 represents Euclidean distance. The method uses
three tolerance levels (0.15m, 0.20m and 0.25m).
The methodology presented in this chapter establishes a comprehensive framework for transforming the complex challenge of collaborative perception into a tractable machine learning
problem. The preprocessing pipeline systematically addresses the fundamental obstacles of
temporal synchronization, spatial alignment, and data quality that have historically limited the
effectiveness of collaborative perception systems. The graph neural network architectures provide complementary approaches to spatial reasoning, each optimized for different aspects of
collaborative perception challenges. The evaluation framework ensures that model performance
is assessed not merely through traditional classification metrics but through robotics-specific

36

Chapter 4 - Methodology

measures that directly relate to operational safety and efficiency. This integrated methodology
forms the foundation for the experimental validation presented in subsequent chapters, providing both theoretical rigor and practical applicability for real-world collaborative robotics
deployment.

5

Experiments and Results

This chapter discusses the experimental setup and assessment of the collaborative perception
framework created in this research. The experiments depict the performance of the preprocessing pipeline and Graph Neural Network (GNN) architectures working in real-world warehouse
settings. They set performance standards for collaborative robotics applications. By evaluating six different GNN models, this research finds the key insights about attention-based and
edge-conditioned methods.

5.1

Experimental Setup

The collaborative perception framework was experimentally evaluated in a well-structured testbed
closely mimicking actual warehouse operations and providing controlled conditions for systematic assessment of the algorithms. The experimental infrastructure includes precision tracking
systems, realistic environmental constraints and advanced robotic platforms. This setup allows
to have a detailed evaluation of collaborative perception algorithms in various operational scenarios.

5.1.1

Warehouse Environment

A set of experiments was conducted in a controlled warehouse environment specifically designed to replicate real-world collaborative robotics scenarios. This sophisticated testbed integrates high-precision motion capture systems (Vicon) with realistic industrial infrastructure to
create that optimal environment for collaborative perception research.
The experimental arena, shown in Figure 5.1, covers a rectangular area of 20.7 m × 9.92 m
(x-axis: -11.1 m to 9.6 m, y-axis: -4.42 m to 5.5 m). This area provides space for two robot
collaborative operations. Inside the arena, five workstations are placed as obstacles to mimic
real warehouse operations. This setup allows collaborative robots to face different scenarios,
such as wide navigation corridors, narrow passages between storage units, and complex multiobstacle areas that are common in today’s automated warehouses. The testbed’s modular design
allows to systematically reconfigure it for various experimental layouts.
The environmental infrastructure includes a Vicon motion capture system which provides submillimeter position accuracy at frequencies of upto 120 Hz. This precise tracking ability lays as
the groundwork for training and validating collaborative perception algorithms. It also allows
for detailed analysis of robot coordination dynamics and spatial relationship modeling.

37

38

Chapter 5 - Experiments and Results

Vicon Camera

Robot 2

Workstations
Robot 1

Figure 5.1: Experimental warehouse arena with workstation and robot platform

5.1.2

Robotic Platform Configuration

The experimental framework uses two autonomous Robomaster platforms. Each one has a set of
sensors designed for working together on perception tasks. The system combines sensing, communication, computation and mobility subsystems to create an advanced collaborative robotics
platform.
Each robotic platform has four key subsystems. The sensing subsystem uses mmWave radar
sensors (IWR6843) to provide high-resolution spatial and velocity measurements. It is further
supported by visual cameras and inertial measurement units for better understanding of the environment. The communication subsystem uses ESP32 Wi-Fi modules which enables real-time
coordination and data sharing between collaborative platforms with low-latency performance.
The computation subsystem runs on NVIDIA Jetson AGX Orin processors. Finally, the mobility subsystem allows omnidirectional navigation with strong obstacle avoidance features,
providing reliable operation in various warehouse settings.

Figure 5.2: Robomaster platform equipped with sensors and motion capture markers

The configuration of the sensors allows the robots to create point cloud data that represents the
obstacles and features of the environment around them. Each robot can sense within its own

39

Chapter 5 - Experiments and Results

range and this distributed sensing is the basis for the next step in robot perception. By sharing
and fusing the data they generate, individual robots can collectively understand the environment
around them, which is far beyond the capabilities of any single platform.
Each experimental trial involves moving both robotic platforms along set paths. During this
time, the system continuously gathers sensor data and ground truth information from the motion
capture setup. The experimental protocol confirms complete coverage of diverse collaborative
scenarios such as cooperative navigation, distributed sensing and dynamic obstacle avoidance
tasks.

5.1.3

Data Collection

Three distinct experimental layouts were designed with varying spatial complexities and collaborative scenarios. Each layout reflects a different operational phase that is typical of warehouse
automation. The three layouts are shown in Figure 5.3.

Layout 1
Arena: 20.7m × 9.9m

6 Workstation: 1.0m × 0.6m

Y Position (m)

4
2

AS_5

0

AS_4

AS_3

2

AS_1
AS_6

4
10

5

0

5

X Position (m)

10

Layout 2

Layout 3
Arena: 20.7m × 9.9m

6 Workstation: 1.0m × 0.6m

4

4

AS_1

2

Y Position (m)

Y Position (m)

Arena: 20.7m × 9.9m

6 Workstation: 1.0m × 0.6m

AS_4
AS_5

0
2

AS_6

4
10

5

AS_3

AS_1

2

AS_4
KLT

0
2

AS_6

AS_5
AS_3

4
0

X Position (m)

5

10

10

5

0

X Position (m)

5

10

Figure 5.3: Arena Layout Configuration
The framework encompasses distinct movement scenarios designed to evaluate collaborative
perception capabilities across varying warehouse automation contexts. These scenarios reflect real-world operational phases commonly encountered in modern automated warehouses,
where multiple autonomous mobile robots must coordinate their sensing and navigation activities within shared workspace environments.
Horizontal scenarios in warehouses involve robots in parallel corridors, coordinating movements and maintaining awareness around shared workstations. This setup enhances collaborative perception due to overlapping sensor ranges. Diagonal scenarios require more complex

40

Chapter 5 - Experiments and Results

navigation at intersecting paths, challenging perception algorithms and offering complementary sensor views from different angles. Vertical scenarios focus on sequential operations along
shared corridors, testing the perception system’s ability to handle temporal offsets and distinguish between current and cleared areas. The dataset extends to hybrid scenarios where robots
combine movement patterns. These include horizontal-diagonal, with one robot in parallel corridors and another at intersecting paths; horizontal-vertical, aligning parallel with sequential
movements; and vertical-horizontal, reversing these patterns. Also, randomized scenarios were
captured, with robots operating freely to challenge perception algorithms in dynamic, unstructured settings.
Overall, The thesis includes 34 individual datasets across three different experimental layouts.
These sessions were captured during an intensive three-month period from February through
May 2025. Each session represents a full collaborative scenario that lasted for 3 to 4 minutes.
They create temporal sequences with enough duration and complexity to train strong Collaborative perception algorithms with GNNs.
Table 5.1: Dataset Summary by Layout
Layout
Layout 1
Layout 2
Layout 3
Total

Dataset
20
9
5
34

For this thesis, each dataset contains one complete recording session with synchronized sensor
data and detailed ground truth annotations. This definition helps to keep the timing consistent,
which is considered to be important for developing collaborative perception algorithms. It also
enables a clear organization across the three experimental layouts.
The preprocessing pipeline ensures that each collaborative dataset contains synchronized radar
point clouds from both robots at 30 Hz. It also includes the use of high-precision Vicon motion
capture data (> 100Hz) that provides robot pose information, environmental context, scenario
metadata and labels for workstations, robots, boundaries and unknown areas. Then, it takes
point cloud data from both robots and combines them into unified spatial voxels. This helps to
create the graph representations that capture true collaborative sensor fusion instead of singlerobot approaches. This dual-robot integration allows for the extraction of new collaborative
features that quantify spatial overlap, coverage redundancy and coordinated perception patterns
that are important for collaborative warehouse automation.

41

Chapter 5 - Experiments and Results

Data Collection
Radar Sensor Data

Robot’s Position
(from Vicon)

Data Preprocessing
(Reference: Figure 5.5)

Model Development
3-Temporal
Window

GATV2 Variants

5-Temporal
Window

ECC Variants

Training Framework
Class Imbalance
Handling
(Weighted Loss)

Optimization:
Adam/AdamW

Regularization:
Dropout,
BatchNorm

Monitoring:
Early Stopping

Evaluation and Analysis
Classification F1,
Acc, Prec, Rec

Spatial,
Distance-based

Performance
vs Efficiency
Analysis

Figure 5.4: Experimental Pipeline

5.2

Preprocessing Implementation

The preprocessing implementation uses the methodology to change raw sensor data into graph
representations for GNN training. The pipeline processes synchronized radar point clouds and
Vicon motion capture data. It converts raw measurements into spatially-aligned datasets that
maintain the spatial relationships necessary for occupancy prediction. This approach overcomes the major challenges of combining data from different sensors in dynamic warehouse
environments. Precise timing and spatial alignment are important for successful collaborative
perception.

42

Chapter 5 - Experiments and Results

Radar Data
Synchronization

Coordinate
Transformation

Filtering/
Quality Enh.

Data
Annotation

Graph
Generation

Vicon Data

Figure 5.5: Data processing pipeline

This framework connects multiple sensor streams from various robotic platforms while keeping
data accurate during the transformation process. The implementation manages the challenges
of heterogeneous sensor data fusion. This includes radar measurements that have different sampling rates, Vicon motion capture data that offers sub-millimeter positioning accuracy, and the
temporal synchronization needed to ensure spatial consistency in collaborative robotic operations. The synchronization process used a fixed Vicon resampling interval of ∆tfixed = 25, ms
and a cross-modal synchronization threshold of δtthresh = 0.5, s. Nearest-neighbor matching
based on absolute timestamp differences |ti − t j | was used to align radar data from both robots
and the Vicon system within these temporal bounds.
6

Layout 3 | Arena: 20.7m × 9.9m
Robot 1 (EP03)
Robot 2 (EP05)

Y Position (m)

4
2
0
2
4
10

5

0

X Position (m)

5

10

Figure 5.6: Parsed Vicon data showing robot trajectory from an experimental run

Coordinate transformation is the basis of the entire preprocessing pipeline. It converts radar
measurements from individual robot frames into a single global coordinate system, using Vicon
data as the reference point. Figure 5.6 shows the precision and continuity of the motion capture
system’s tracking abilities throughout an experimental run. This provides the spatial backbone
that allows precise coordinate transformation of all sensor measurements. The smooth, continuous trajectory indicates successful tracking without any interruptions or positioning errors.
This is crucial for maintaining the spatial integrity of the transformed sensor data.
The transformation process includes complex mathematical operations that takes into account
the robot’s dynamic movements such as position updates, changes in orientation and the temporal synchronization requirements. The trajectory data showcases movement patterns typical
of collaborative robotics, including coordinated maneuvers, overlapping coverage areas, and
dynamic positioning changes that characterize real-world scenarios. This detailed spatial information helps the preprocessing pipeline accurately transform sensor measurements while
considering the dynamic nature of the collaborative workspace.

43

Chapter 5 - Experiments and Results

Figure 5.7: Coordinate Transformed Point cloud Left: Robot 1. Right: Robot 2.

Figure 5.7 shows successful transformation outcomes with radar point clouds from both the
robots aligned in the global frame. The left panel presents Robot 1’s sensor data, while the right
panel displays Robot 2’s measurements, both now aligned within the same global reference
frame. Blue and red clusters signify distinct measurements from each robot, telling how the
transformation process maintains the spatial distribution of sensor detections while providing a
unified spatial view of the collaborative workspace.
The visualization highlights the complementary nature of collaborative sensor coverage, where
each robot adds unique spatial data which then enhances the overall perception of the system.
The successful alignment confirms the coordinate transformation’s accuracy and ensures that
the spatial relationships between sensor detections have been maintained throughout the transformation process. The different clustering patterns in each robot’s data reflect varying perspectives and sensor orientations, illustrating how collaborative perception achieves comprehensive
workspace coverage that single sensor systems cannot achieve.

Figure 5.8: Point Cloud Cleaning Comparison. Left: Original data. Right: Cleaned data

The data quality enhancement phase utilises the advanced filtering techniques to boost the
signal-to-noise ratio while keeping the integrity of true target detections. Figure 5.8 dramatically shows the filtering effectiveness, transforming noisy raw sensor data into clean, processed
measurements that are suitable for graph neural network training. The left panel shows the
original data, which has visible noise artifacts, spurious detections, and environmental clutter
typical of raw radar measurements in complex indoor settings. The right panel displays the
cleaned data after applying the filtering process, demonstrating much less noise while keeping
the important spatial structure of true target detections.
The filtering process takes a multi-layered approach to address the challenges of radar sensor
data in warehouse environments. The signal-to-noise ratio filtering part uses a minimum SNR

44

Chapter 5 - Experiments and Results

threshold of 5.0 dB, effectively getting rid of low-quality measurements while keeping highconfidence detections. Spatial boundary filtering limits the workspace to specific operational
boundaries, with an X-range from -10.0 to 10.5 meters and a Y-range from -5.0 to 6.0 meters,
ensuring measurements match the actual collaborative workspace. Height filtering restricts detections to the operational zone between 0.05 and 2.5 meters, which removes ground reflections
and ceiling artifacts.
Statistical outlier detection is the most advanced part of the filtering process. It uses k-nearest
neighbor analysis with k set to 20 neighbors and a standard deviation threshold of 3.0. This
method calculates the mean distance to the nearest neighbors for each point and finds outliers
based on statistical deviation from the global distribution. The field-of-view filtering limits detections to a 120-degree angle, ensuring measurements come from the intended sensor coverage
area.

Figure 5.9: Annotated Dataset

The data annotation implementation generated accurate ground truth labels from processed sensor measurements. Figure 5.9 shows the labeling achieved with the help of the annotation implementation. It illustrates how raw sensor measurements have been organized into meaningful
occupancy categories. The visualization uses different colors to represent various classes, including workstations, boundaries, robots, and unknown/unoccupied areas. This provides the
essential ground truth for supervised learning in graph neural networks.
Table 5.2: Class Distribution Analysis
Annotation Class
Count Percentage Binary Label
Workstation
247,847
52.30%
1
Boundary
35,667
7.53%
1
Robot
9,305
1.96%
1
KLT Container
111
0.02%
1
Occupied Subtotal
292,930
61.81%
1
Unknown/Unoccupied 180,981
38.19%
0
Total Dataset
473,911
100.00%
–
Class Imbalance Ratio: 0.618:1 (Unoccupied:Occupied)

Binary Class
Occupied

Unoccupied
–

A statistical analysis of the collaborative dataset was conducted to measure class imbalance and
find suitable weighting parameters for optimizing the loss function. The entire dataset consists

45

Chapter 5 - Experiments and Results

of 473,911 annotated data points spread across warehouse layouts and scenarios. Table 5.2
offers a detailed breakdown of the class distribution in the collaborative perception dataset,
highlighting the spatial sparsity typical of warehouse environments.
The analysis shows that occupied areas make up 292,930 voxels (61.8%), while unoccupied
areas comprise 180,981 voxels (38.2%). In the occupied category, workstation areas account
for 52.3% of the total dataset, indicating the warehouse’s focus on manufacturing operations.
Based on this analysis, the positive weight for the weighted loss function is calculated as w pos =
Nunoccupied /Noccupied = 180, 981/292, 930 = 0.618. This results in a class imbalance ratio of
0.618:1, which needs methodological intervention to ensure balanced learning performance.
Implementing the weighted loss function involves calculating class weights and applying them
to the BCEWithLogitsLoss function. Specifically, the implementation uses pos weight =
torch.tensor([0.618]).to(device). This weighted loss approach aims to reduce model
bias and improve generalization capabilities across different warehouse operational scenarios,
preventing major class overfitting and ensuring steady performance throughout the collaborative
perception framework.

5.2.1

Graph Construction

The graph generation converts synchronized point cloud measurements from two robots into
organized graph representations. These graphs capture spatial relationships and robot cooperation patterns essential for occupancy prediction tasks. Each frame processes combined point
clouds with robot source tracking. This allows for novel collaboration features that highlight
individual robot contributions within shared spatial areas. The 0.1m voxel resolution strikes
a good balancebetween spatial detail preservation and computational tractability. Processing
times increase linearly with the complexity of the dual-robot input.
The voxelization merges radar points from both robots into unified spatial voxels while maintaining robot source identification. Each voxel collects spatial data from both robots and computes collaboration features that measure individual contributions, spatial overlap patterns and
coverage redundancy. These elements are important for robust perception. Voxel label assignment uses majority voting from all robot contributions. This ensures consistent class labeling
while leveraging improved spatial coverage from dual-robot sensor fusion.
The graph representation employs spatial voxels as nodes, with each 0.1m³ voxel containing
radar points from Robot 1, Robot 2, or both. The fully connected edge layout creates complete
graphs where each node links to every other node within a frame. This enables thorough modeling of spatial relationships and global message passing. Complete connectivity allows Graph
Neural Networks to capture local spatial patterns and long-range dependencies, both crucial
for collaborative perception tasks. The enhanced representation of node features reflects both
spatial relationships and collaboration patterns through 17 dimensional vectors. These vectors
include normalized positions, raw coordinates, relative positions, distances, and novel robot
collaboration features.

46

Chapter 5 - Experiments and Results
Table 5.3: Graph Component Specifications
Component
Nodes (V )
Edges (E)
Node Features

Node Labels
Adjacency Matrix

Definition
Spatial voxels containing radar points
from one or both robots
Fully connected topology: all nodes connect to all other nodes
[spatial features(13),
robot counts(2),
collaborative score(1),
temporal offset(1)]
Classes:
0=unoccupied, 1=occupied
(Workstation, Robot, Boundary)
Symmetric binary matrix with complete
connectivity (excluding self-loops)

Dimensions
|V | ∈ [2, 500]
|E| =

|V |(|V |−1)
2

R17

{0, 1}
{0, 1}|V |×|V |

Spatial features (indices 0-12) encode normalized position coordinates, raw spatial coordinates,
position relative to the frame center and origin, and distance to the spatial center. Robot collaboration features (indices 13-15) measure the contributions of Robot 1 and Robot 2 within each
spatial voxel. This allows Graph Neural Networks to model cooperation patterns and spatial
coverage redundancy. For temporal configurations (T3 and T5), an additional temporal offset feature (index 16) indicates the causal position within past sequences. This keeps a strict
temporal order for real-time deployment scenarios.
Table 5.4: Node Feature Definitions
Feature
Normalized coordinates
Global coordinates
Relative coordinates
Origin coordinates
Distance to center
Robot 1 & 2 point count
Collaboration score
Temporal offset

Description
X, Y, Z normalized to frame bounds [0,1]
Raw X, Y, Z in global reference frame
X, Y, Z relative to frame center
X, Y, Z relative to coordinate origin
Euclidean distance to frame center
Number of points from Robot 1 & 2 in voxel
Multi-robot collaboration metric [0, 0.5]
Temporal sequence position (T3/T5 only)

1.1

1.1

1.2

1.2

1.2

1.3
1.4

1.54.8 4.7 4.6 4.5 4.4 4.3
X Position (m)

Y Position (m)

1.1
Y Position (m)

Y Position (m)

Index
0-2
3-5
6-8
9-11
12
13-14
15
16

1.3
1.4

1.54.8 4.7 4.6 4.5 4.4 4.3
X Position (m)

unoccupied
occupied

1.3
1.4
1.54.8 4.7 4.6 4.5 4.4 4.3
X Position (m)

Figure 5.10: Graph structure visualization showing node connectivity and labels
The following adjacency matrix shows the fully connected layout using actual GNN frame data

47

Chapter 5 - Experiments and Results

(file: 1746370721.936099.pt from the temporal 5 dataset). This complete graph has 5 voxels
and a total of 20 connections, with 4 occupied voxes and 1 unoccupied voxel (Figure 5.10).

0
1

A5×5 = 
1
1
1

1
0
1
1
1

1
1
0
1
1

1
1
1
0
1


1
1

1

1
0

The matrix shows symmetry (Ai j = A ji ), confirming undirected edges. It has no self-loops (Aii =
0) and gets an average degree of 9.0, which shows full connectivity between all node pairs.
Each node processes 17-dimensional features, including spatial coordinates, relative positions,
distance measurements, robot contribution counts and temporal offset indicators.

5.2.2

Temporal Integration

The implementation of different temporal window configurations changes the structure and
availability of training data in the collaborative perception framework. The temporal modeling method includes single-frame processing (T1), three-frame sequences (T3), and five-frame
sequences (T5). Each of these has specific requirements for contextual information, which directly affects the total number of training samples. This phenomenon occurs because longer
temporal sequences require more historical context to keep the temporal dependencies intact.
As a result, there are fewer complete sequences for model training and evaluation.
Table 5.5: Temporal Window Impact on Frame Availability
Layout
Layout 1
Layout 2
Layout 3
Total

Temporal Window 1
7,138 frames
8,454 frames
4,323 frames
19,915

Temporal Window 3
7,098 frames (-40)
8,436 frames (-18)
4,313 frames (-10)
19,847

Temporal Window 5
7,058 frames (-80)
8,418 frames (-36)
4,303 frames (-20)
19,779

Overall, the impact across all layouts shows that while the reduction in available training data is
measurable, it stays within acceptable limits for effective model development. The full dataset
contains 19,915 frames for single-frame processing. This number drops to 19,847 frames for
three-frame sequences and 19,779 frames for five-frame sequences. The largest decrease is only
136 frames, which is about 0.68% of the total dataset. This slight reduction allows all temporal
modeling approaches to maintain enough statistical power for reliable performance evaluation
while keeping the necessary temporal dependencies intact for collaborative perception tasks.

5.2.3

Dataset Partitioning

The dataset partitioning strategy used in this collaborative perception framework focuses on preventing temporal information leakage while ensuring a good distribution of data across training,
validation, and testing stages. Instead of randomly assigning individual frames or sequences,

48

Chapter 5 - Experiments and Results

the partitioning algorithm works at the dataset level. It guarantees that complete recording sessions go to specific splits. This method is crucial for maintaining the temporal integrity of the
collaborative perception data as it helps to avoid situations where the model learns patterns
that cross different evaluation phases, which could result in inflated performance metrics and a
weaker generalization assessment.
Table 5.6: Dataset Split Distribution
Layout
Layout 1
Layout 2
Layout 3
Total

Training
14 datasets
6 datasets
2 datasets
22 datasets

Validation
3 datasets
1 dataset
1 dataset
5 datasets

Testing
3 datasets
2 datasets
2 datasets
7 datasets

Total
20 datasets
9 datasets
5 datasets
34 datasets

This dataset-level partitioning approach makes sure that no temporal information from individual recording sessions overlaps between different splits. This separation is crucial for evaluating
collaborative perception algorithms without bias. The careful distribution maintains variety in
layout across all partitions. This prevents any single experimental setup from overshadowing
the evaluation process. It also provides enough representation for dependable statistical analysis
across various collaborative perception scenarios.
Table 5.7: Perception Frame Distribution Across Splits
Split
Training
Validation
Testing
Total

Datasets
22 (64.7%)
5 (14.7%)
7 (20.6%)
34 (100%)

T1 Frames
14,356
2,944
2,615
19,915

T3 Frames
14,312
2,934
2,601
19,847

T5 Frames
14,268
2,924
2,587
19,779

The resulting frame distribution across different temporal configurations shows the steady influence of the dataset-level partitioning strategy while keeping the effects of the temporal windows
that were established earlier. The training partition has 14,356 frames for single-frame processing, 14,312 frames for three-frame sequences, and 14,268 frames for five-frame sequences.
In total, there are 42,936 frames across all temporal setups, which is 72.1% of the combined
dataset. This large allocation for training offers strong statistical power for model learning
while also ensuring that the number of available frames decreases systematically as the size
of the temporal window increases. The validation partition contains 2,944, 2,934, and 2,924
frames for the corresponding temporal configurations, adding up to 8,802 frames or 14.8% of
the total dataset. This provides enough data for reliable tuning of hyperparameters and model
selection for all time modeling approaches.
The testing partition features 2,615, 2,601 and 2,587 frames for the three temporal windows,
totaling 7,803 frames or 13.1% of the complete dataset. This distribution guarantees thorough
evaluation capabilities while maintaining the temporal dependency constraints inherent in each
window configuration. The consistent percentage reduction across temporal windows in each
split indicates that the partitioning strategy maintains the essential traits of temporal modeling
needs while ensuring balanced representation for reliable comparative analysis.

49

Chapter 5 - Experiments and Results

The importance of this partitioning framework goes beyond simple data distribution considerations. It addresses the core requirements for evaluating collaborative perception. By ensuring
temporal independence between splits, while also maintaining layout diversity and temporal
window consistency, the framework lays a solid foundation for unbiased assessments of collaborative perception algorithms across various temporal modeling strategies, spatial settings, and
interaction patterns.

5.3

GNN Architecture Implementation

Six different Graph Neural Network architectures were implemented and tested for collaborative perception tasks. These include Standard GATv2, Complex GATv2, and Edge-Conditioned
Convolution (ECC). Each architecture used two temporal window configurations, Temporal3 and Temporal-5, to examine how temporal modeling affects spatial reasoning performance.
The architectures vary in complexity, number of parameters, and attention mechanisms while
keeping the same input-output specifications for comparative evaluation.

5.3.1

Standard GATv2 Architecture Implementation

The Standard GATv2 architecture uses multi-head attention mechanisms for graph-based spatial reasoning tasks. It processes 17-dimensional node features through three GATv2 layers,
maintaining consistent 64-dimensional hidden representations.
Table 5.8: Standard GATv2 - Architecture Specifications
Layer
Input
GATv2-1
GATv2-2
GATv2-3
BatchNorm
MLP Hidden
Classifier

Operation
Node Embedding
Multi-Head Attention
Multi-Head Attention
Multi-Head Attention
Normalization
Linear + ReLU
Linear

Input
17
64
64
64
64
64
128

Output
64
64
64
64
64
128
1

Heads Dropout
0.0
4
0.2
4
0.2
4
0.2
0.5
0.0
Total Parameters:

Parameters
1,088
8,448
8,448
8,448
771
8,256
65
35,524

This design strikes a good balance between model capability and computational needs for collaborative perception tasks. It employs four attention heads per layer, allowing the model to
recognize various spatial relationship patterns simultaneously while maintaining computational
tractability. This multi-head attention mechanism helps the model focus on different aspects
of spatial relationships in each layer, such as proximity-based connections, similarities, and
directional relationships between nodes that represent voxelized workspace regions.
The three-layer architecture provides enough depth for learning hierarchical spatial features
while avoiding excessive computational overhead that could hinder real-time deployment. The
consistent 64-dimensional hidden representation across the attention layers keeps the model
expressive for spatial reasoning while ensuring memory efficiency. With a parameter count of

50

Chapter 5 - Experiments and Results

35,524, this architecture is well-suited for resource-limited collaborative robotics applications
where multiple models might need to operate simultaneously.
The dropout regularization strategy, set at 0.2 for attention layers and 0.5 for the MLP hidden
layer, is designed to prevent overfitting on the spatial reasoning task while allowing enough
model capacity to learn complex spatial patterns. The batch normalization layer following
the attention stack enhances training stability and convergence, which is particularly important
when dealing with irregular graph structures that vary in size and connectivity. The mean pooling aggregation strategy effectively summarizes node-level spatial representations into graphlevel features, maintaining spatial context and enabling binary occupancy classification through
the final linear classifier.

5.3.2

Complex GATv2 Architecture Implementation

The Complex GATv2 architecture is a high-capacity model that captures complex spatial relationships in collaborative perception scenarios. The increased hidden dimensionality of 128
greatly boosts its representational capacity compared to the standard version. This allows the
model to learn more detailed spatial features and intricate interaction patterns from collaborative robot observations. The eight attention heads in each layer enable the model to focus on
various types of spatial relationships simultaneously, such as geometric proximity, similarity,
temporal consistency, and collaboration patterns.
Table 5.9: Complex GATv2 - Architecture Specifications
Layer
Input
GATv2-1
BatchNorm-1
GATv2-2
BatchNorm-2
GATv2-3
BatchNorm-3
GATv2-4
BatchNorm-4
LayerNorm
MLP Hidden
Classifier

Operation
Node Embedding
Multi-Head Attention
Batch Normalization
Multi-Head Attention
Batch Normalization
Multi-Head Attention
Batch Normalization
Multi-Head Attention
Batch Normalization
Layer Normalization
Linear + ReLU
Linear

Input
17
128
128
128
128
128
128
128
128
128
128
128

Output
128
128
128
128
128
128
128
128
128
128
256
1

Heads Dropout
0.0
8
0.3
8
0.3
8
0.3
8
0.3
0.5
0.0
Total Parameters:

Parameters
2,176
33,280
513
33,280
513
33,280
513
33,280
513
1,024
32,896
129
171,397

With four layers, the model enables hierarchical learning of spatial features. Early layers capture local relationships, like immediate neighborhood patterns and basic geometric shapes. In
contrast, deeper layers integrate global spatial context and learn complex multi-scale spatial dependencies. This hierarchy is especially useful for collaborative perception tasks, where grasping both local occupancy patterns and the global workspace structure is essential for accurate
spatial reasoning.
The normalization strategy, which uses batch normalization after each attention layer and layer
normalization before the final MLP, greatly improves training stability and convergence. This

51

Chapter 5 - Experiments and Results

is crucial for the Complex GATv2 architecture due to its large number of parameters and the
irregular nature of graph-structured spatial data. The batch normalization layers stabilize attention weight distributions across different graph sizes and connectivity patterns. Meanwhile,
layer normalization ensures consistent feature scaling before the final classification.
The higher dropout rate of 0.3 in the attention layers balances the model’s increased capacity
and helps prevent overfitting on spatial reasoning tasks. The larger MLP hidden layer, with
256 dimensions and 0.5 dropout, improves feature transformation before binary classification.
This allows the model to develop more complex decision boundaries for occupancy prediction. Even with a significant increase in parameters, the Complex GATv2 architecture remains
computationally feasible while offering improved spatial reasoning capabilities for challenging
collaborative perception scenarios.

5.3.3

ECC Architecture Implementation

The Edge-Conditioned Convolution architecture uses a message passing method for spatial reasoning in collaborative perception tasks with fully connected graph structures. Unlike attentionbased methods, the ECC architecture handles spatial relationships through direct message passing between all node pairs. This allows for better integration of spatial information across the
entire graph structure.
Table 5.10: ECC - Architecture Specifications
Layer
Input
ECC-1
BatchNorm-1
ECC-2
BatchNorm-2
ECC-3
BatchNorm-3
GlobalPool
Classifier

Operation
Node Embedding
Edge-Conditioned Conv
Batch Normalization
Edge-Conditioned Conv
Batch Normalization
Edge-Conditioned Conv
Batch Normalization
Mean Aggregation
Linear

Input
17
64
64
64
64
64
64
64
128

Output
64
64
64
64
64
64
64
128
1

Dropout Activation
0.0
–
0.2
ReLU
–
–
0.2
ReLU
–
–
0.2
ReLU
–
–
–
–
0.0
Sigmoid
Total Parameters:

Parameters
1,088
139,392
257
139,392
257
139,392
257
0
129
420,164

The ECC implementation features a graph topology where every node is linked to every other
node. This design allows spatial information to flow directly between any two points in the collaborative workspace. To pass messages between connected nodes, the architecture combines
the features of source and target nodes and processes them with shared neural networks. This
concatenation-based approach helps the model learn spatial relationships through the combined
representation of node feature pairs, without needing to compute geometric relationships or
distances explicitly.
With 420,164 parameters, the model reflects the complexity of the neural networks that learn
to process concatenated node feature pairs and create meaningful spatial representations. The
three-layer ECC architecture allows for the gradual refinement of spatial features through several rounds of message passing. Each layer lets nodes gather information from all other nodes in
the graph, with deeper layers understanding more complex spatial dependencies and multi-hop

52

Chapter 5 - Experiments and Results

relationships. The consistent 64-dimensional hidden representation throughout the architecture
balances expressiveness with computational tractability for the fully connected graph structure.
Batch normalization layers after each ECC operation enhance training stability. This stability
is crucial due to the dense connectivity and high message volume in fully connected graphs.
The mean aggregation method in the global pooling layer effectively summarizes the spatial
representations learned through the message passing process. The sigmoid activation in the
final classifier produces a probabilistic output for binary occupancy prediction, with the fully
connected architecture ensuring that classification decisions take into account spatial context
from the entire collaborative workspace.

5.3.4

Training Infrastructure

Model training took place on a high-performance PC with an NVIDIA GeForce RTX 4070 GPU
(device 0) that has 12 GB GDDR6X memory. GPU acceleration was enabled through CUDA
12.6, which ensured compatibility with deep learning frameworks.
The computational environment used Python 3.12.8 along with PyTorch 2.7.0 and PyTorch Geometric 2.6.1 frameworks, supplemented by NumPy 2.2.2, Pandas 2.2.3, and Scikit-learn 1.6.1
for data processing and analysis. GPU utilization was optimized with mixed precision training,
using torch.cuda.amp.autocast() to lower memory consumption while keeping numerical stability. The PyTorch setup supported CUDA 12.6 with cuDNN version 90501 for faster tensor
operations.
Standardized training procedures were followed across all graph neural network architectures
to ensure reproducible results and fair performance comparisons. Training settings included
early stopping with a 20-epoch patience based on validation loss, cosine annealing learning rate
scheduling with warm restarts, and adaptive batch sizes that adjusted between 8-32 graphs depending on model complexity and available GPU memory (11.6 GB effective capacity). Memory management was optimized effectively with gradient accumulation strategies and periodic
garbage collection to avoid memory fragmentation during long training sessions.
Architecture-specific optimization strategies were utilized based on hyperparameter tuning through
grid search evaluation. GATv2 variants employed the Adam optimizer with learning rates between [0.001, 0.003], beta parameters (0.9, 0.999), and an epsilon value of 1e-8 for steady convergence. ECC architectures used the AdamW optimizer with a fixed learning rate of 0.0005,
a weight decay coefficient of 0.01 for L2 regularization and the identical beta parameters to
enhance convergence stability and improve generalization performance.
Training convergence monitoring was implemented by logging training and validation metrics
at each epoch. Automatic model checkpointing occurred whenever there was an improvement
in validation loss, which helped preserve optimal model weights and prevent overfitting. To
account for differences in model performance from random initialization and stochastic training processes, GAT model variants were trained 2-3 times with different random seeds. The
best-performing model based on validation metrics was then selected for final evaluation and
comparison.

53

5.3.5

Chapter 5 - Experiments and Results

Architecture Comparison Summary
Table 5.11: GNN Architecture Comparison Summary

Architecture
Standard GATv2
Complex GATv2
ECC

Parameters (T3/T5)
35.5K
171.4K
420.2K

Layers
3
4
3

Attention Heads
4
8
–

Hidden Dimensions
64
128
64

The three architectures showcase different methods for graph-based spatial reasoning, each
with its own level of complexity. Standard GATv2 offers good computational efficiency with
a moderate number of parameters. Complex GATv2 increases representational capacity by
using a deeper architecture and more attention heads. ECC brings in novel edge-conditioned
processing to model spatial relationships adaptively. All architectures handle the identical input
features, including spatial coordinates, temporal information, and occupancy indicators, thereby
allowing for direct performance comparisons among various graph neural network methods.

5.4

Comprehensive Model Evaluation and Analysis

This section presents comprehensive evaluation results for all six Graph Neural Network architectures implemented for collaborative robot occupancy prediction. The evaluation employs a
multi-dimensional assessment framework combining traditional classification metrics, detailed
confusion matrix analysis, and advanced spatial evaluation methodology. This systematic approach provides definitive architectural ranking and recommendations for collaborative robotics
systems.

5.4.1

Confusion Matrix Analysis

The confusion matrices for all the evaluated GNN models give clear insights into classification
behavior and error patterns from different architectural approaches. These matrices show the
distribution of true positives, true negatives, false positives, and false negatives for occupancy
prediction. This allows for a thorough analysis of each model’s strengths and weaknesses in
collaborative perception scenarios.

38.3%

Unoccupied

Occupied

Unoccupied

6.8%

17.7%

37.0%

3.6%

41.6%

Unoccupied

Occupied

True Label

29.0%

Occupied

25.8%

True Label

Unoccupied

Chapter 5 - Experiments and Results

Occupied

54

Predicted Label

Predicted Label

(a) Standard GATv2 with T=3

(b) Standard GATv2 with T=5

Figure 5.11: Confusion matrices for Standard GATv2 models.
The analysis of confusion matrices highlights unique classification behavior patterns across the
three architectural families. Each one is optimized for different collaborative perception needs.
Standard GATv2 models show the best overall performance characteristics. This model’s classification behavior is very sensitive to occupied spaces while maintaining reasonable specificity.
This trait indicates a bias toward thorough occupancy detection, which is especially important
in safety-critical collaborative robotics applications. In these cases, failing to detect occupied
spaces is riskier than generating occasional false alarms.

Unoccupied

Occupied

Predicted Label

(a) ECC with T=3

Unoccupied

37.3%

25.1%

12.4%

33.1%

Unoccupied

Occupied

True Label

9.6%

29.4%

Occupied

Unoccupied

32.3%

True Label

20.8%

Occupied

The impact of the temporal window, analyzed through confusion matrices, reveals changes in
classification behavior for different temporal configurations. Standard GATv2 T5 shows even
more aggressive occupancy detection. However, this heightened sensitivity leads to a higher
false positive rate of 37%. The temporal extension seems to capture additional motion patterns, which enhance occupancy detection but also introduce more spatial uncertainty, resulting
in increased false alarm rates. This trade-off suggests that T5 configurations may be best for
applications that focus on thorough detection rather than precision, such as initial safety assessments or obstacle avoidance systems.

Predicted Label

(b) ECC with T=5

Figure 5.12: Confusion matrices for Edge-Conditioned Convolution (ECC) models.

55

Chapter 5 - Experiments and Results

Unoccupied

Occupied

Predicted Label

(a) Complex GATv2 with T=3

Unoccupied

25.4%

18.6%

18.0%

26.7%

Unoccupied

Occupied

True Label

19.7%

36.8%

Occupied

Unoccupied

14.6%

True Label

40.3%

Occupied

ECC models show unique classification patterns that result from their edge-conditioned spatial
reasoning approach. The T3 variant shows strong occupancy detection. The edge-conditioned
convolution mechanism allows for flexible modeling of spatial relationships, effectively capturing local neighborhood patterns, which leads to reliable positive case detection. However,
this model has higher false positive rates compared to Complex GATv2. This suggests that the
adaptive filtering approach sometimes generalizes too much regarding spatial occupancy patterns. The T5 ECC variant has better precision, showing that extended temporal context helps
stabilize edge-conditioned processing and reduces spatial ambiguity.

Predicted Label

(b) Complex GATv2 with T=5

Figure 5.13: Confusion matrices for Complex GATv2 models.

Complex GATv2 models display the most conservative classification behavior across both temporal configurations, highlighting their high-precision design features. The T3 variant has
strong specificity, recording the lowest false positive rate among all models evaluated. This
conservative pattern stems from the model’s deeper architecture, which includes eight attention
heads and batch normalization layers. These elements seem to support more clear decisionmaking boundaries. However, this improvement in precision leads to higher false negative rates.
This indicates that the model’s conservative approach results in missed occupancy detections,
which can be critical in safety-related applications needing clear spatial awareness.
The false positive analysis of all models uncovers important traits for collaborative robotics.
Standard GATv2 models have moderate false positive rates that balance detection ability with
operational efficiency. In contrast, Complex GATv2 models reduce false alarms but risk missing
occupancy detections. ECC models fall in the middle, offering competitive detection capabilities while keeping false alarm rates manageable. These trends suggest that model choice should
reflect specific application needs, with Standard GATv2 fitting for general collaborative perception tasks, Complex GATv2 suitable for precision-critical applications, and ECC models ideal
for situations requiring flexible spatial reasoning capabilities.

5.4.2

Classification Metrics

Figure 5.14 shows the classification performance for all evaluated models. It highlights clear
architectural hierarchies and patterns suitable for the collaborative robotics framework in this

56

Chapter 5 - Experiments and Results

thesis.

Standard GATv2 T=3

0.6815

0.6417

0.5691

0.8492

Standard GATv2 T=5

0.6717

0.5934

0.5290

0.9197

0.85
0.80

ECC T=3

0.6405

0.5809

0.5362

0.7952

ECC T=5

0.6386

0.6250

0.5687

0.7281

Performance Score

Models (Ranked by F1 Score)

0.90

0.75
0.70
0.65

Complex GATv2 T=3

0.5968

0.6567

0.6350

0.5630

Complex GATv2 T=5

0.5935

0.6347

0.5974

0.5897

Accuracy

Precision

Recall

F1 Score

Performance Metrics

0.60
0.55

Figure 5.14: Comprehensive Performance Summary of GNN Models in a Heatmap
The performance comparison offers several important insights for collaborative perception.
Standard GATv2 T3 achieves the highest F1-score of 68.15%, making it the best architecture
for these tasks. This model balances precision at 56.91% and recall at 84.92%. This balance
indicates strong detection abilities while keeping false positives relatively low. The model’s
strong performance comes from its efficient three-layer design with four attention heads per
layer. This structure provides enough capacity for representation without adding complexity
that could lead to overfitting on the spatial reasoning task.
The temporal window analysis shows a consistent architectural preference across all models,
with 3-frame temporal windows outperforming 5-frame configurations. Standard GATv2 has a
slight performance decline going from T3 to T5 configurations, with F1-scores dropping from
68.15% to 67.17%. However, it maintains excellent recall performance at 91.97% in the T5
variant. This suggests that shorter temporal contexts effectively capture motion patterns for collaborative perception without the complexity and potential noise introduced by extended temporal histories. The finding supports the need for real-time deployment, where computational
efficiency is vital for autonomous robotics applications.
The architecture-specific analysis reveals distinct performance traits suited for different collaborative scenarios. Complex GATv2 models achieve the highest precision scores, with the T3
variant hitting 63.50%. They work well in applications that need minimal false positives, but
this comes with a significant drop in recall performance to 56.30%. The Complex GATv2 architecture’s four-layer design with eight attention heads and batch normalization enhances its
discriminative ability. However, the increased complexity tends to make predictions patterns
more conservative, focusing on precision instead of comprehensive occupancy detection.
On the other hand, ECC models show consistent moderate performance across both temporal

57

Chapter 5 - Experiments and Results

configurations, with T3 and T5 variants achieving F1-scores of 64.05% and 63.86%, respectively. The ECC architecture uses edge-conditioned convolution to adaptively model spatial
relationships, generating dynamic filters based on local neighborhood characteristics. This
method particularly excels in recall performance, with the T3 variant achieving 79.52% recall, indicating strong positive case detection capabilities. The edge-conditioned processing
enables the model to adjust its spatial reasoning based on local geometric relationships, ensuring robust performance across various spatial configurations typical in collaborative robotics
environments.
The successful performance of all six model variants confirms the effectiveness of the improved
feature representation framework developed in this research. The 17-dimensional node features,
including spatial coordinates, temporal offset information and occupancy indicators, provide
enough data for effective graph-based spatial reasoning. Consistent performance across different architectures shows the robustness of the collaborative perception framework for various
scenarios, where different computational resources and performance needs may require distinct
architectural choices.
Performance differences between temporal windows offer key insights into optimal deployment
configurations for real-time collaborative systems. The steady advantage of T3 windows across
all architectures, with an average F1-score improvement of 1.2 percentage points over T5 configurations, indicates that collaborative perception works best within shorter temporal horizons.
This finding shows that immediate spatial relationships and recent motion patterns deliver more
relevant information than longer temporal histories for occupancy prediction tasks. This result
has practical implications for real-time system design, allowing for reduced computational overhead while maintaining the perception quality needed for autonomous collaborative robotics.
Figure 5.15 provides an analysis of precision-recall trade-offs for all evaluated architectures. It
offers visual insights into model behavior and optimal performance features for collaborative
perception tasks.

Precision vs Recall Trade-off

F1 Score Comparison
Standard GATv2 T=3

0.675

Precision

0.650

Complex GATv2 T=5
ECC T=5

0.575
0.550
0.525
0.500
0.5

F1=0.55

0.6

Standard GATv2 T=3
F1=0.7

ECC T=3
F1=0.65

F1=0.6

0.7

0.6815
0.6717

Standard GATv2 T=5

Complex GATv2 T=3

0.625
0.600

F1=0.6

0.700

0.8

Recall

0.9

ECC T=3

0.6405

ECC T=5

0.6386

Complex GATv2 T=3

Standard GATv2 T=5

Complex GATv2 T=5
1.0

0.5968
0.5935
0.56 0.58 0.60 0.62 0.64 0.66 0.68 0.70

F1 Score

Figure 5.15: Precision-Recall Trade-off Analysis and F1-Score Comparison.

The precision-recall trade-off analysis exhibits various behaviour patterns in architecture, which
affects how we can use collaborative robotics. Complex GATv2 models cluster in the highprecision area, achieving 59.74-63.50% precision but showing conservative recall performance
at 56.30-58.97%. This suggests that they focus on reducing false positives, which makes them

58

Chapter 5 - Experiments and Results

more suitable for applications where minimizing false alarms is critical. On the other hand,
Standard GATv2 models occupy the high-recall area with recall rates of 84.92-91.97% and
moderate precision at 52.90-56.91%. This reflects an aggressive approach to occupancy detection, which is important for safety-critical applications that need thorough spatial awareness.
Standard GATv2 T3 strikes the best balance, achieving the highest F1-score of 68.15%. It is positioned near the F1=0.7 contour line and displays strong performance for general collaborative
perception tasks. ECC models display intermediate behavior, with balanced precision-recall
features indicated by F1-scores of 64.05% and 63.86%. This reflects their adaptable edgeconditioned processing abilities. The analysis of temporal windows confirms T3’s superiority
across all architectures. Standard GATv2 T3 outperforms its T5 counterpart by 0.98%, which
indicates that focused temporal attention is more effective than a broader temporal context in
spatial reasoning tasks.
This analysis shows that having a complex architecture does not always lead to better results.
The simpler Standard GATv2 architecture consistently outperforms the more complicated options. The metrics and confusion matrix evaluation identify Standard GATv2 T3 as the best
model for collaborative robotics applications. It provides the right balance of detection sensitivity needed for safety-critical operations while keeping the necessary operational precision
for real-world use. The model’s reliable performance across different temporal windows supports its architectural design and positions it as the choice for collaborative robotics perception
systems.
To illustrate the practical implications of our analysis, Figure 5.16 shhows the 3D graph predictions from all six models. This visualization depicts that the most parameter-efficient model,
Standard GATv2 maintains better prediction quality while using fewer computational resources
than more complex models. The F1 scores shown here are specifically calculated for this data
and differ from the overall evaluation metrics.

59

Chapter 5 - Experiments and Results

Occupied
Unoccupied
0.6

Z (m)

0.4
0.2
0.0

Y(

m)

4.0
3.5
Nodes: 20
3.0Occ: 11
X (m-3.8
) -3.6 -3.4 2.5 Unocc: 9

-4.4-4.2
-4.0

(a) Ground Truth

0.4

0.4

0.2

0.2

0.0

0.0

Z (m)

0.6

0.4

0.4

0.2

0.2

0.0

0.0

-4.4-4.2
-4.0

4.0

Y(

m)

3.5 20
Nodes:
3.0Occ: 15
X (m-3.8
) -3.6 -3.4 2.5 Unocc: 5

(f) Complex GATv2 T=3,
F1=0.6154

Z (m)

0.6

-4.4-4.2
-4.0

4.0

3.5 20
Nodes:
3.0Occ: 10
X (m-3.8
) -3.6 -3.4 2.5Unocc: 10

m)

0.0

m)

(d) ECC T=3, F1=0.8000

Y(

0.2

Y(

4.0

3.5 20
Nodes:
3.0 Occ: 9
X (m-3.8
) -3.6 -3.4 2.5Unocc: 11

Z (m)

Z (m)

0.4

(e) ECC T=5, F1=0.8182

0.0
-4.4-4.2
-4.0

Y(

Y(

0.6

0.2

m)

(c) Standard GATv2 T=5,
F1=0.9091

0.4

Y(

(b) Standard GATv2 T=3,
F1=0.7619

m)

4.0
-4.4-4.2
3.5 20
Nodes:
-4.0
3.0Occ: 11
X (m-3.8
) -3.6 -3.4 2.5 Unocc: 9

m)

4.0
-4.4-4.2
3.5 20
Nodes:
-4.0
3.0Occ: 10
X (m-3.8
) -3.6 -3.4 2.5Unocc: 10

4.0
-4.4-4.2
3.5 20
Nodes:
-4.0
3.0Occ: 11
X (m-3.8
) -3.6 -3.4 2.5 Unocc: 9

0.6

Z (m)

0.6

Z (m)

0.6

(g) Complex GATv2 T=5,
F1=0.5714

Figure 5.16: 3D Graph-Based Occupancy Prediction. Ground truth scene (top) compared
against predictions from various GNN models with different temporal windows (T ). Subfigures (b)–(g) show comparative results, annotated with model type, temporal window, and F1
score.

The 3D visualization supports the numerical findings. Standard GATv2 models gives the best
balance of prediction accuracy and computing efficiency. Even with fewer parameters than ECC
models, Standard GATv2 can provide better F1 performance on this representative sample. This
makes it the best choice for situations where resources are limited.

60

5.4.3

Chapter 5 - Experiments and Results

Spatial Accuracy Results

Table ?? shows the spatial accuracy evaluation results for all models and tolerance levels. The
performance metrics reveal major differences in spatial reasoning capabilities among various
GNN architectures.
Table 5.12: Spatial accuracy and mean distance error for each model across tolerance levels
Model
Standard GATv2 T3

Standard GATv2 T5

ECC T3

ECC T5

Complex GATv2 T3

Complex GATv2 T5

Tolerance (m)

Accuracy (%)

Mean Error (cm)

0.15
0.20
0.25
0.15
0.20
0.25
0.15
0.20
0.25
0.15
0.20
0.25
0.15
0.20
0.25
0.15
0.20
0.25

75.4
76.0
79.7
71.1
84.0
84.1
63.7
70.8
77.4
52.0
65.8
77.8
77.5
69.3
65.4
63.7
64.8
67.7

5.5
6.8
7.6
4.7
5.9
6.5
7.3
8.0
8.5
8.3
9.2
11.0
5.1
10.6
13.0
8.2
11.4
12.2

The spatial accuracy results highlight clear performance variations across models and tolerance
levels. Standard GATv2 T5 achieves the highest spatial accuracy at standard navigation tolerance (84.0% at 0.20m) and robust operation tolerance (84.1% at 0.25m). It also keeps low
distance errors ranging from 4.7 to 6.5 centimeters across all tolerance levels. Standard GATv2
T3 maintains a consistent performance across tolerance levels, with spatial accuracy increasing
from 75.4% at high precision (0.15m) to 79.7% at robust operation (0.25m). The distance errors
remain relatively stable, ranging from 5.5 to 7.6 centimeters.
The ECC models exhibits marked improvement as tolerance relaxes. ECC T3 increases from
63.7% accuracy at 0.15m tolerance to 77.4% at 0.25m tolerance, with distance errors from 7.3 to
8.5 centimeters. ECC T5 demonstrates even greater improvement, starting at 52.0% accuracy
for high precision and reaching 77.8% for robust operation. However, it has higher distance
errors that range from 8.3 to 11.0 centimeters.
Complex GATv2 models have inconsistent patterns. Complex GATv2 T3 achieves good highprecision performance (77.5% at 0.15m) but drops significantly at relaxed tolerances (65.4%
at 0.25m), with distance errors growing from 5.1 to 13.0 centimeters. Complex GATv2 T5

61

Chapter 5 - Experiments and Results

shows more stable but generally lower performance, with spatial accuracy ranging from 63.7%
to 67.7% and distance errors between 8.2 and 12.2 centimeters.
Overall, Standard GATv2 T3 offers a more consistent performance across all tolerance levels,
having smallest distance error variance, while Standard GATv2 T5 displays the superior performance for standard and robust operations.

5.4.4

Temporal Window Comparative Analysis

The temporal window configuration represents a fundamental design choice in collaborative
perception systems, directly influencing the model’s ability to capture temporal dependencies
and motion patterns. Table 5.13 presents a systematic head-to-head comparison between 3frame and 5-frame temporal configurations across all architectural families, providing critical
insights into the optimal temporal context for different GNN architectures.
Table 5.13: Temporal Window Head-to-Head Performance Comparison
Architecture
Standard GATv2
Complex GATv2
ECC

Window
T3
T5
T3
T5
T3
T5

Accuracy (%)
73.04
73.21
72.84
69.12
68.47
70.15

Precision (%)
61.24
62.87
59.35
57.24
56.92
58.34

Recall (%)
78.72
71.45
60.12
61.85
73.28
69.87

F1-Score (%)
68.15
67.17
59.68
59.35
64.05
63.86

The comparative analysis shows the clear differences in how various architectural families respond to temporal sensitivity patterns. Standard GATv2 models exhibits a balanced performance with only slight drops in effectiveness between temporal configurations. The 3-frame
version achieves better recall performance at 78.72% and an F1-score of 68.15%, indicating it
is more sensitive to occupied areas. Meanwhile, the 5-frame version has small gains in accuracy
at 73.21% and precision at 62.87%.
Complex GATv2 architectures shows the strongest sensitivity to the choice of temporal window. The 3-frame setup consistently beats the 5-frame version across various metrics, reaching
72.84% accuracy, 59.35% precision and a 59.68% F1-score. This suggests that complex architectures may experience overfitting when given a longer temporal context.
Edge-Conditioned Convolution models dusplays an interesting split in their temporal behavior.
The 5-frame version achieves better overall accuracy at 70.15% and precision at 58.34%, but
the 3-frame setup leads in recall performance at 73.28% and an F1-score of 64.05%. This tells
that ECC architectures benefit from longer temporal contexts when it comes to precision tasks
but may lose sensitivity to occupied areas with longer sequences.
The analysis of temporal windows indicates that shorter contexts (3-frame) usually offer better
recall across all architectures, showing greater sensitivity to occupied regions. However, longer
contexts (5-frame) tend to improve precision, highlighting a key trade-off in deployment scenarios where minimizing false negatives is crucial compared to situations that prioritize reducing
false positives.

62

5.4.5

Chapter 5 - Experiments and Results

Computational Efficiency and Real-Time Performance Analysis

The thorough evaluation of computational efficiency and real-time performance across all tested
Graph Neural Network architectures shows important insights for use in collaborative robotics
environments. Analyzing parameter efficiency highlights the link between model complexity
and performance effectiveness. Meanwhile, latency measurements confirm practical deployment for real-time warehouse operations.
Table 5.14 presents F1 score for every 1000 parameters, offering clear insights into the costs
and benefits of computation. This is crucial for environments with limited resources.
Table 5.14: Parameter Efficiency Analysis of GNN Architectures
Model Architecture
Standard GATv2
Standard GATv2
Complex GATv2
Complex GATv2
ECC
ECC

Temporal Window
T3
T5
T3
T5
T3
T5

F1 Score
0.682
0.672
0.597
0.594
0.641
0.639

Parameters (k)
35.5
35.5
171.4
171.4
420.2
420.2

F1/1k Params
0.0192
0.0189
0.0035
0.0035
0.0015
0.0015

Standard GATv2 models show excellent parameter efficiency, with ratios over 0.018. They
achieve the highest F1 performance while keeping computational overhead low at 35,500 parameters. Complex GATv2 architectures have moderate efficiency at around 0.0035, needing
4.8 times more parameters than Standard models, yet they deliver lower performance. ECC architectures demonstrate the lowest efficiency at 0.0015, using 11.8 times more parameters than
Standard GATv2 and having poorer F1 scores. The Standard GATv2 T3 configuration stands
out as the best choice for deployment. It offers 12.6 times better parameter efficiency than ECC
models and 5.5 times better than Complex GATv2 versions. This high efficiency makes Standard GATv2 well-suited for real-time collaborative perception applications, where performance
and computational limits are important.
Real-time performance characteristics were evaluated through a detailed latency analysis of
both preprocessing tasks and model inference, by performing 100 preprocessing measurements
and 200 inference tests to set reliable performance benchmarks for production use. The preprocessing pipeline has a total latency of 20 milliseconds which includes data synchronization,
converting graphs from point clouds to PyTorch Geometric structures, and preparing tensors.
Graph conversion is the main computational task, taking 16 milliseconds and including voxelization, feature creation, edge generation, and tensor conversio
The Standard GATv2 T3 model exhibits great inference performance, with an average latency
of 5 milliseconds across different graph sizes. Latency distribution is consistent, with the 95th
percentile at 6 milliseconds and the 99th percentile at 11 milliseconds, indicating steady performance under varying computational demands. The complete end-to-end system runs with a
total latency of 25 milliseconds, providing a 2 times safety margin compared to real-time needs
and allowing for 40 FPS processing capability.
This performance profile sets up a framework that can be quickly used in production warehouse
environments. It also points out clear ways to improve through better graph generation algorithms. The preprocessing-to-inference ratio of 4:1 suggests that optimization efforts should

63

Chapter 5 - Experiments and Results

focus on graph conversion algorithms, especially spatial indexing techniques, to get the best
performance for collaborative robotics applications. The counterintuitive link between model
complexity and efficiency shows that simpler designs often lead to better practical performance
in systems with limited resources for collaborative perception.
The evaluation framework verifies the effectiveness of graph-based methods work for collaborative perception. It also shows the architectural features that help identify the best deployment
scenarios. The consistent advantage of simpler architectures over more complex ones shows
that collaborative perception gains more from effective spatial relationship modeling than from
complex architectural designs. This offers useful insights for developing future systems in collaborative warehouse settings.

6

Conclusion and Future Work

This chapter synthesizes the key findings from the development and evaluation of a collaborative perception management layer for 6G-enabled future robotic systems. The research contributions are summarized, limitations acknowledged and promising directions for future work
are outlined to advance the field of collaborative robotics in warehouse automation.

6.1

Summary of Contributions and Research Impact

This dissertation comprehensively developed and validated a framework of collaborative perception specifically for warehouse robotics, using mmWave radar as a proof-of-concept for
future 6G integrated sensing and communication capabilities. The research addressed major
shortcomings in collaborative perception systems by introducing systematic methodological innovations and thorough experimental validation.
The main contribution is the development of a novel preprocessing pipeline that transforms
raw sensor data from multiple robotic platforms into structured graph representations. These
representations are suitable for machine learning algorithms. This pipeline overcomes major
challenges in collaborative perception such as temporal synchronization, coordinate transformation, data quality improvement and data labeling. The synchronisation framework efficiently
manages varying sensor update rates while maintaining the spatial integrity required for collaborative perception tasks. The coordinate transformation method allows for the smooth integration of sensor measurements from multiple robot-centric reference frames into a single global
representation.
The second major contribution is the thorough inspection of Graph Neural Network architectures, which are made for collaborative spatial reasoning. This evaluation was done systematically across six different GNN variants. Performance of these different architectures was
then used to establish the clear architectural hierarchies for occupancy prediction in warehouse
environments. Standard GATv2 with 3-frame temporal windows proved to be the best architecture, reaching a 68.15% F1-score while keeping impressive parameter efficiency at 0.0192 F1
per 1000 parameters. This finding counters the typical assumptions surrounding model complexity, showing that less complex structures frequently provide better real-world outcomes in
resource-constrained collaborative perception systems.
Collaborative features developed in these Graph Neural Networks model dual-robot sensor fusion patterns instead of treating collaborative perception as independent single-robot observations. This represents a major step forward in GNNs and their potential applications.These
features measure the contributions of individual robots within shared spatial voxels. They also
64

65

Chapter 6 - Conclusion and Future Work

show coverage redundancy, which is important for strong warehouse operations. The experimental framework encompasses evaluation methodologies that reach well beyond traditional
classification metrics, incorporating spatial accuracy evaluation with distance-based assessments involves using tolerance levels.
The implications of the research findings are substantial for the use of collaborative robots in
industrial settings. The shown mmWave radar performance for collaborative perception offers
a solid base for the future 6G integrated sensing and communication systems. Simpler GNN
architectures outperform their more complex counterparts, which suggests that the advantages
of collaborative perception stem not from architectural intricacy but from efficient modeling of
spatial relationships. The real-time performance analysis confirms system deployability with a
total latency of 25 milliseconds allowing for 40 FPS processing capability.
While this research meets its main goals, several limitations must be addressed. These experiments were conducted in a controlled laboratory environment with predefined workstation
layouts and limited robot movement patterns. However, additional challenges in real-world
warehouse settings include dynamic inventory levels, electromagnetic interference, and unpredictable human-robot interactions. The collaborative perception framework currently supports
only two robotic platforms, limiting the ability to assess scalability for larger robot swarms that
are common in modern warehouse operations.

6.2

Future Work and Research Directions

The foundational approach of this research created several promising pathways for improving
collaborative perception in warehouse robotics. These directions handles existing limitations
and broaden the framework’s abilities for future autonomous warehouse systems.
Scalable architectures are an important area of research for managing large robot swarms without computational performance degradation. Future work should look into hierarchical graph
representations, distributed processing frameworks and effective communication protocols for
large-scale collaborative perception with more than 10 agents. Improved attention mechanisms
that can dynamically focus on relevant groups of collaborating robots based on their spatial
proximity could greatly enhance scalability. Using federated learning methods could allow collaborative perception systems to learn from different robot experiences while protecting privacy
and reducing communication overhead.
The shift from mmWave radar proof-of-concept to real 6G ISAC needs a lot of research into
joint optimization of sensing and communication resources. Future work should focus on resource allocation strategies that adjusts to balance sensing accuracy and communication quality
based on operational needs. Improved sensor fusion which includes cameras, LiDAR, ultrasonic sensors and mmWave radar within the collaborative perception framework could greatly
enhance robustness and accuracy. Creating 6G-native collaborative perception protocols that
use combined sensing and communication capabilities could transform warehouse automation
by optimizing the use of shared spectrum resources.
Expanding the framework to go beyond binary occupancy prediction and include understanding of objects is a major opportunity for advancement. Collaborative robots could use object
detection and instance tracking to distinguish between various kinds of obstacles, predict movement patterns and coordinate complex manipulation tasks. By creating task-aware perception

66

Chapter 6 - Conclusion and Future Work

systems that can change the focus on the robot’s current goals could greatly improve efficiency
for warehouse tasks like inventory management and order fulfillment.
Validation in real warehouse environments with actual industrial operations is the next important
step for practical deployment. This involves long-term studies of system reliability, maintenance
needs and performance degradation over time in dynamic industrial settings. Collaboration
with industrial partners can give access to real operational scenarios that becomes essential
for technology transfer. Developing standardized benchmarking frameworks for collaborative
perception in warehouse robotics will speed up research progress through open datasets and
standardized evaluation metrics.
As collaborative perception systems become more advanced, research into safety verification
and failure mode analysis is gaining high importance. Future work should look into formal
verification methods for collaborative perception algorithms and create safety-critical design
principles. Developing explainable AI approaches for collaborative perception could boost trust
and acceptance in industrial settings. This would help operational personnel to understand how
collaborative perception systems make specific decisions.

6.3

Concluding Remarks

This research lays a firm base for collaborative perception. It provides both theoretical and practical answers to the automation challenges in warehouses. Graph Neural Networks can effectively model collaborative spatial reasoning. Meanwhile, the sensing capabilities of mmWave
radar technology make it suitable for industrial environments.
The counterintuitive finding that simpler GNN architectures perform better than more complex
ones shows the importance of empirical validation in robotics research. It suggests that modeling spatial relationships efficiently is more important than having sophisticated designs for
collaborative perception tasks. Combining temporal modeling, spatial reasoning, and collaborative features in a single framework offers a guide for future studies in collaborative perception
systems.
The shift to 6G-enabled collaborative robotics marks a major change in how autonomous systems see and interact with their surroundings. The collaborative perception framework created
in this research places warehouse robotics at the leading edge of this change. It allows for
the next generation of intelligent, coordinated and efficient autonomous logistics systems. The
methods, experimental findings and design suggestions in this thesis provides the robotics community useful tools and information to improve collaborative perception technologies.
The future of warehouse automation depends on successfully combining advanced sensing technologies, intelligent perception algorithms and next-generation communication systems. This
thesis marks an important step toward that future. It offers immediate practical benefits and
outlines a plan for ongoing improvements in collaborative robotics and autonomous warehouse
systems.

Bibliography
[1] Peter R. Wurman, Raffaello D’Andrea, and Mick Mountz. “Coordinating Hundreds of
Cooperative, Autonomous Vehicles in Warehouses”. In: AI Magazine 29.1 (2008), pp. 9–
19. DOI: 10.1609/aimag.v29i1.2082.
[2] Roland Siegwart, Illah R. Nourbakhsh, and Davide Scaramuzza. Introduction to Autonomous Mobile Robots. 2nd ed. Cambridge, MA: MIT Press, 2011. ISBN: 978-0-26201535-6.
[3] Daniel Mellinger, Nathan Michael, and Vijay Kumar. “Trajectory Generation and Control for Precise Aggressive Maneuvers with Quadrotors”. In: The International Journal
of Robotics Research 31.5 (2012), pp. 664–674. DOI: 10.1177/0278364911434236.
[4] Irfan Fachrudin Priyanta et al. “Evaluation of LoRa technology for vehicle-to-infrastructure
communication in urban environments”. In: IEEE Access 11 (2023), pp. 89452–89465.
DOI : 10.1109/ACCESS.2023.3305421.
[5] Quan Zhou, Hao Zhang, and Ming Li. “Collaborative perception for multi-robot systems:
A survey”. In: Robotics and Computer-Integrated Manufacturing 79 (2023), p. 102432.
DOI : 10.1016/j.rcim.2022.102432.
[6] Ross A. Knepper et al. “IkeaBot: An autonomous multi-robot coordinated furniture assembly system”. In: 2013 IEEE International Conference on Robotics and Automation.
2013, pp. 855–862. DOI: 10.1109/ICRA.2013.6630673.
[7] Irfan Fachrudin Priyanta et al. “Evaluation of LoRa technology for vehicle-to-infrastructure
communication in urban environments”. In: IEEE Access 11 (2023), pp. 89452–89465.
[8] Fan Liu et al. “Integrated sensing and communications: Toward dual-functional wireless
networks for 6G and beyond”. In: IEEE Journal on Selected Areas in Communications
40.6 (2022), pp. 1728–1767. DOI: 10.1109/JSAC.2022.3156632.
[9] J. Andrew Zhang et al. “Enabling joint communication and radar sensing in mobile networks—A survey”. In: IEEE Communications Surveys & Tutorials 24.1 (2022), pp. 306–
345. DOI: 10.1109/COMST.2021.3122519.
[10] Yulun Tian et al. “Kimera-Multi: A system for distributed multi-robot metric-semantic
simultaneous localization and mapping”. In: IEEE International Conference on Robotics
and Automation. 2021, pp. 11210–11218.
[11] Xiangyu Wang et al. “Integrated sensing and communication: Recent advances and ten
open challenges”. In: IEEE Internet of Things Journal 10.11 (2023), pp. 9416–9440.
[12] J. Hasch et al. “Millimeter-Wave Technology for Automotive Radar Sensors in the 77
GHz Frequency Band”. In: IEEE Transactions on Microwave Theory and Techniques
60.3 (2012), pp. 845–860. DOI: 10.1109/TMTT.2011.2178427.

I

II

BIBLIOGRAPHY

[13] S. M. Patole et al. “Automotive Radars: A Review of Signal Processing Techniques”. In:
IEEE Signal Processing Magazine 34.2 (2017), pp. 22–35. DOI: 10.1109/MSP.2016.
2636418.
[14] C. Iovescu and S. Rao. “The fundamentals of millimeter wave radar sensors”. In: IEEE
Radar Applications Series 11.2 (2021), pp. 78–92. DOI: 10.1109/RA.2021.3045004.
[15] G. M. Brooker. “Mutual Interference of Millimeter-Wave Radar Systems”. In: IEEE
Transactions on Electromagnetic Compatibility 49.1 (2020), pp. 170–181. DOI: 10 .
1109/TEMC.2006.888168.
[16] Shu Sun, Athina P. Petropulu, and H. Vincent Poor. “MIMO Radar for Advanced Driver
Assistance Systems and Autonomous Driving: Advantages and Challenges”. In: IEEE
Signal Processing Magazine 37.4 (2020), pp. 98–117. DOI: 10 . 1109 / MSP . 2020 .
2975760.
[17] Yifan Wang, Guoqiang Cheng, and Yi Zeng. “mmWave Radar Point Cloud Segmentation
Using GMM in Multimodal Traffic Monitoring”. In: IEEE Transactions on Intelligent
Transportation Systems 22.12 (2021), pp. 7840–7849. DOI: 10 . 1109 / TITS . 2020 .
3029159.
[18] I. F. Priyanta et al. “Towards 6G-Driven Digital Continuum in Logistics”. In: 2023 IEEE
International Conference on Communications Workshops (ICC Workshops). 2023, pp. 1–
6. DOI: 10.1109/ICCWorkshops56927.2023.10322238.
[19] Giuseppe Fragapane et al. “Planning and control of autonomous mobile robots for intralogistics: Literature review and research agenda”. In: European Journal of Operational
Research 294.2 (2021), pp. 405–426. DOI: 10.1016/j.ejor.2021.01.019.
[20] Lynne E. Parker. “Distributed intelligence: Overview of the field and its application in
multi-robot systems”. In: Journal of Physical Agents 2.1 (2008), pp. 5–14. DOI: 10 .
14198/JoPha.2008.2.1.02.
[21] Ashley Stroupe et al. “Behavior-based multi-robot collaboration for autonomous construction tasks”. In: IEEE/RSJ International Conference on Intelligent Robots and Systems. 2005, pp. 1495–1500. DOI: 10.1109/IROS.2005.1545243.
[22] Stergios I. Roumeliotis and George A. Bekey. “Distributed multirobot localization”. In:
IEEE Transactions on Robotics and Automation 18.5 (2002), pp. 781–795. DOI: 10 .
1109/TRA.2002.803461.
[23] Pierre-Yves Lajoie et al. “DOOR-SLAM: Distributed, online, and outlier resilient SLAM
for robotic teams”. In: IEEE Robotics and Automation Letters 5.2 (2020), pp. 1656–1663.
DOI : 10.1109/LRA.2020.2967681.
[24] Hugh Durrant-Whyte and Tim Bailey. “Simultaneous localization and mapping: part I”.
In: IEEE Robotics & Automation Magazine 13.2 (2006), pp. 99–110. DOI: 10.1109/
MRA.2006.1638022.
[25] Patrik Schmuck and Margarita Chli. “Multi-UAV collaborative monocular SLAM”. In:
IEEE International Conference on Robotics and Automation. 2017, pp. 3863–3870. DOI:
10.1109/ICRA.2017.7989445.
[26] Qi Chen et al. “Collaborative perception for autonomous driving: Current status and future trend”. In: IEEE Transactions on Intelligent Transportation Systems 20.8 (2019),
pp. 2892–2909. DOI: 10.1109/TITS.2018.2868372.

III

BIBLIOGRAPHY

[27] Runsheng Xu et al. “V2X-ViT: Vehicle-to-everything cooperative perception with vision
transformer”. In: IEEE Transactions on Intelligent Vehicles 7.4 (2022), pp. 793–803. DOI:
10.1109/TIV.2022.3141254.
[28] Yiming Li et al. “Learning distilled collaboration graph for multi-agent perception”. In:
Advances in Neural Information Processing Systems. Vol. 34. 2021, pp. 29541–29552.
[29] Yen-Cheng Liu et al. “When2com: Multi-agent perception via communication graph
grouping”. In: IEEE/CVF Conference on Computer Vision and Pattern Recognition. 2020,
pp. 4106–4115. DOI: 10.1109/CVPR42600.2020.00416.
[30] Tsun-Hsuan Wang et al. “V2VNet: Vehicle-to-vehicle communication for joint perception and prediction”. In: IEEE Transactions on Pattern Analysis and Machine Intelligence
44.10 (2020), pp. 6209–6223. DOI: 10.1109/TPAMI.2020.3033769.
[31] Mark A. Richards, James A. Scheer, and William A. Holm. Principles of Modern Radar:
Basic Principles. SciTech Publishing, 2010. ISBN: 978-1891121524.
[32] Merrill I. Skolnik. Radar Handbook. 3rd. McGraw-Hill Education, 2008.
0071485470.

ISBN :

978-

[33] Ole Schumann et al. “Semantic segmentation on radar point clouds”. In: 21st International Conference on Information Fusion. 2018, pp. 2179–2186. DOI: 10.23919/ICIF.
2018.8455344.
[34] Bence Major et al. “Vehicle detection with automotive radar using deep learning on
range-azimuth-doppler tensors”. In: arXiv preprint arXiv:1904.08789 (2019).
[35] Andras Palffy et al. “CNN based road user detection using the 3D radar cube”. In: IEEE
Robotics and Automation Letters 5.2 (2020), pp. 1263–1270. DOI: 10.1109/LRA.2020.
2967272.
[36] Jonathan Brookshire and Seth Teller. “Extrinsic calibration from per-sensor egomotion”.
In: Robotics: Science and Systems. 2012, pp. 504–511. DOI: 10 . 15607 / RSS . 2012 .
VIII.063.
[37] Jesse Levinson and Sebastian Thrun. “Unsupervised calibration for multi-beam lasers”.
In: Experimental Robotics (2013), pp. 179–193. DOI: 10.1007/978- 3- 319- 000657_13.
[38] Zonghan Wu et al. “A comprehensive survey on graph neural networks”. In: IEEE Transactions on Neural Networks and Learning Systems 32.1 (2021), pp. 4–24. DOI: 10.1109/
TNNLS.2020.2978386.
[39] Jie Zhou et al. “Graph neural networks: A review of methods and applications”. In: AI
Open 1 (2020), pp. 57–81. DOI: 10.1016/j.aiopen.2021.01.001.
[40] Michael M. Bronstein et al. “Geometric deep learning: Going beyond Euclidean data”.
In: IEEE Signal Processing Magazine 34.4 (2017), pp. 18–42. DOI: 10 . 1109 / MSP .
2017.2693418.
[41] Peter W. Battaglia et al. “Relational inductive biases, deep learning, and graph networks”.
In: arXiv preprint arXiv:1806.01261 (2018).
[42] Thomas N. Kipf and Max Welling. “Semi-supervised classification with graph convolutional networks”. In: arXiv preprint arXiv:1609.02907 (2016).
[43] Petar Veličković et al. “Graph attention networks”. In: International Conference on Learning Representations. 2018.

IV

BIBLIOGRAPHY

[44] Shaked Brody, Uri Alon, and Eran Yahav. How Attentive are Graph Attention Networks?
2022. arXiv: 2105.14491 [cs.LG]. URL: https://arxiv.org/abs/2105.14491.
[45] Martin Simonovsky and Nikos Komodakis. “Dynamic edge-conditioned filters in convolutional neural networks on graphs”. In: Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition. 2017, pp. 3693–3702.
[46] Justin Gilmer et al. “Neural message passing for quantum chemistry”. In: Proceedings of
the 34th International Conference on Machine Learning. 2017, pp. 1263–1272.
[47] Charles R. Qi et al. “PointNet: Deep learning on point sets for 3D classification and
segmentation”. In: IEEE Conference on Computer Vision and Pattern Recognition. 2017,
pp. 652–660. DOI: 10.1109/CVPR.2017.16.
[48] Charles R. Qi et al. “PointNet++: Deep hierarchical feature learning on point sets in a
metric space”. In: Advances in Neural Information Processing Systems. 2017, pp. 5099–
5108.
[49] Changan Chen et al. “Gated residual recurrent graph neural networks for traffic prediction”. In: AAAI Conference on Artificial Intelligence. 2019, pp. 485–492. DOI: 10.1609/
aaai.v33i01.3301485.
[50] Guohao Li et al. “DeepGCNs: Can GCNs go as deep as CNNs?” In: arXiv preprint
arXiv:1904.03751 (2019).
[51] Yue Wang et al. “Dynamic graph CNN for learning on point clouds”. In: ACM Transactions on Graphics. Vol. 38. 5. 2019, pp. 1–12. DOI: 10.1145/3326362.
[52] Sajad Saeedi et al. “Multiple-robot simultaneous localization and mapping: A review”.
In: Journal of Field Robotics 33.1 (2016), pp. 3–46. DOI: 10.1002/rob.21620.
[53] Cesar Cadena et al. “Past, present, and future of simultaneous localization and mapping: Toward the robust-perception age”. In: IEEE Transactions on Robotics 32.6 (2016),
pp. 1309–1332. DOI: 10.1109/TRO.2016.2624754.
[54] Siddharth Choudhary et al. “Distributed mapping with privacy and communication constraints: Lightweight algorithms and object-based models”. In: International Journal of
Robotics Research. Vol. 36. 12. 2017, pp. 1286–1311. DOI: 10.1177/0278364917732640.
[55] Yulun Tian et al. “Kimera-Multi: Robust, distributed, dense metric-semantic SLAM for
multi-robot systems”. In: IEEE Transactions on Robotics. Vol. 38. 4. 2022, pp. 2022–
2038. DOI: 10.1109/TRO.2021.3137751.
[56] Antoni Rosinol et al. “Kimera: From SLAM to spatial perception with 3D dynamic scene
graphs”. In: International Journal of Robotics Research 40.12-14 (2021), pp. 1510–1546.
DOI : 10.1177/0278364921105658.
[57] Ammar Gharaibeh et al. “Smart cities: A survey on data management, security, and
enabling technologies”. In: IEEE Communications Surveys & Tutorials 19.4 (2017),
pp. 2456–2501. DOI: 10.1109/COMST.2017.2736886.
[58] Runsheng Xu et al. “CoBEVT: Cooperative bird’s eye view semantic segmentation with
sparse transformers”. In: Conference on Robot Learning. 2022, pp. 1812–1823.
[59] Liam Paull et al. “AUV navigation and localization: A review”. In: IEEE Journal of
Oceanic Engineering 39.1 (2014), pp. 131–149.
[60] Markus Windolf, Nils Götzen, and Michael Morlock. “Systematic accuracy and precision
analysis of video motion capturing systems—exemplified on the Vicon-460 system”. In:
Journal of Biomechanics 41.12 (2008), pp. 2776–2780.

V

BIBLIOGRAPHY

[61] Morgan Quigley et al. “ROS: an open-source Robot Operating System”. In: ICRA Workshop on Open Source Software. Vol. 3. 3.2. 2009, p. 5.
[62] Jonathan Kelly and Gaurav S. Sukhatme. “Visual-inertial sensor fusion: Localization,
mapping and sensor-to-sensor self-calibration”. In: The International Journal of Robotics
Research 30.1 (2011), pp. 56–79.
[63] G. Ajay Kumar et al. “LiDAR and Camera Fusion Approach for Object Distance Estimation in Self-Driving Vehicles”. In: Symmetry 12.2 (2020), p. 324. DOI: 10.3390/
sym12020324. URL: https://doi.org/10.3390/sym12020324.
[64] Dzmitry Bahdanau, Kyunghyun Cho, and Yoshua Bengio. “Neural Machine Translation
by Jointly Learning to Align and Translate”. In: arXiv preprint arXiv:1409.0473 (2014).
[65] Will Hamilton, Zhitao Ying, and Jure Leskovec. “Inductive Representation Learning on
Large Graphs”. In: Advances in Neural Information Processing Systems. Ed. by I. Guyon
et al. Vol. 30. Curran Associates, Inc., 2017. URL: https://proceedings.neurips.
cc / paper _ files / paper / 2017 / file / 5dd9db5e033da9c6fb5ba83c7a7ebea9 Paper.pdf.

List of Figures

2.1

FMCW radar signal processing pipeline [14] . . . . . . . . . . . . . . . . . . .

6

2.2

Maximum angular field of view determination [14] . . . . . . . . . . . . . . .

8

2.3

Texas Instruments IWR6843 ISK Module . . . . . . . . . . . . . . . . . . . .

9

4.1

Methodology Framework for Collaborative Perception . . . . . . . . . . . . .

19

4.2

PCL to Graph Conversion Pipeline. . . . . . . . . . . . . . . . . . . . . . . . .

26

4.3

Attention mechanisms comparison: (a) Standard GAT with static attention, (b)
GATv2 with dynamic attention [44]. . . . . . . . . . . . . . . . . . . . . . . .

29

5.1

Experimental warehouse arena with workstation and robot platform . . . . . .

38

5.2

Robomaster platform equipped with sensors and motion capture markers . . . .

38

5.3

Arena Layout Configuration . . . . . . . . . . . . . . . . . . . . . . . . . . .

39

5.4

Experimental Pipeline . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

41

5.5

Data processing pipeline . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

42

5.6

Parsed Vicon data showing robot trajectory from an experimental run . . . . . .

42

5.7

Coordinate Transformed Point cloud Left: Robot 1. Right: Robot 2. . . . . . .

43

5.8

Point Cloud Cleaning Comparison. Left: Original data. Right: Cleaned data . .

43

5.9

Annotated Dataset . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

44

5.10 Graph structure visualization showing node connectivity and labels . . . . . . .

46

5.11 Confusion matrices for Standard GATv2 models. . . . . . . . . . . . . . . . .

54

5.12 Confusion matrices for Edge-Conditioned Convolution (ECC) models. . . . . .

54

5.13 Confusion matrices for Complex GATv2 models. . . . . . . . . . . . . . . . .

55

5.14 Comprehensive Performance Summary of GNN Models in a Heatmap . . . . .

56

5.15 Precision-Recall Trade-off Analysis and F1-Score Comparison.

57

. . . . . . . .

VII

LIST OF FIGURES
5.16 3D Graph-Based Occupancy Prediction. . . . . . . . . . . . . . . . . . . . . .

59

List of Tables

4.1

Confusion Matrix Terminology for Occupancy Prediction . . . . . . . . . . . .

34

4.2

Evaluation Metrics for Voxel-wise Classification . . . . . . . . . . . . . . . . .

35

5.1

Dataset Summary by Layout . . . . . . . . . . . . . . . . . . . . . . . . . . .

40

5.2

Class Distribution Analysis . . . . . . . . . . . . . . . . . . . . . . . . . . . .

44

5.3

Graph Component Specifications . . . . . . . . . . . . . . . . . . . . . . . . .

46

5.4

Node Feature Definitions . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

46

5.5

Temporal Window Impact on Frame Availability . . . . . . . . . . . . . . . .

47

5.6

Dataset Split Distribution . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

48

5.7

Perception Frame Distribution Across Splits . . . . . . . . . . . . . . . . . . .

48

5.8

Standard GATv2 - Architecture Specifications . . . . . . . . . . . . . . . . . .

49

5.9

Complex GATv2 - Architecture Specifications . . . . . . . . . . . . . . . . . .

50

5.10 ECC - Architecture Specifications . . . . . . . . . . . . . . . . . . . . . . . .

51

5.11 GNN Architecture Comparison Summary . . . . . . . . . . . . . . . . . . . .

53

5.12 Spatial accuracy and mean distance error for each model across tolerance levels

60

5.13 Temporal Window Head-to-Head Performance Comparison . . . . . . . . . . .

61

5.14 Parameter Efficiency Analysis of GNN Architectures . . . . . . . . . . . . . .

62

List of Abbreviations
6G

Sixth Generation wireless technology

Adam

Adaptive Moment Estimation

AdamW Adam with Weight Decay
AI

Artificial Intelligence

AMR

Autonomous Mobile Robot

AS

Assembly Station

BCE

Binary Cross-Entropy

CFAR

Constant False Alarm Rate

CNN

Convolutional Neural Network

CPPS

Cyber-Physical Production Systems

CUDA

Compute Unified Device Architecture

DoF

Degrees of Freedom

DSP

Digital Signal Processor

ECC

Edge-Conditioned Convolution

FFT

Fast Fourier Transform

FMCW Frequency Modulated Continuous Wave
FN

False Negative

FoV

Field of View

FP

False Positive

FPGA

Field-Programmable Gate Array

FPS

Frames Per Second

GATv2 Graph Attention Network version 2
GCN

Graph Convolutional Network
IX

X
GNN

Graph Neural Network

GPS

Global Positioning System

GPU

Graphics Processing Unit

GraphSAGE Graph Sample and Aggregate
IF

Intermediate Frequency

IMU

Inertial Measurement Unit

IoT

Internet of Things

ISAC

Integrated Sensing and Communication

KLT

Kleine Ladungsträger (Small Load Carrier)

LeakyReLU Leaky Rectified Linear Unit
LiDAR Light Detection and Ranging
MIMO Multiple-Input Multiple-Output
MLP

Multi-Layer Perceptron

mmWave millimeter-wave
MUSIC Multiple Signal Classification
NumPy Numerical Python
PC

Personal Computer

PCL

Point Cloud Library

ReLU

Rectified Linear Unit

ROS

Robot Operating System

SLAM

Simultaneous Localization and Mapping

SNR

Signal-to-Noise Ratio

TN

True Negative

TP

True Positive

ULA

Uniform Linear Array

URLLC Ultra-Reliable Low-Latency Communication
V2I

Vehicle-to-Infrastructure

V2N

Vehicle-to-Network

V2P

Vehicle-to-Pedestrian

V2V

Vehicle-to-Vehicle

V2X

Vehicle-to-Everything

Eidesstattliche Versicherung
(Affidavit)

Name, Vorname

Matrikelnummer

(surname, first name)

(student ID number)

Bachelorarbeit

Masterarbeit

(Bachelor’s thesis)

(Master’s thesis)

Titel
(Title)

Ich versichere hiermit an Eides statt, dass ich die
vorliegende Abschlussarbeit mit dem oben genannten
Titel selbstständig und ohne unzulässige fremde Hilfe
erbracht habe. Ich habe keine anderen als die
angegebenen Quellen und Hilfsmittel benutzt sowie
wörtliche und sinngemäße Zitate kenntlich gemacht.
Die Arbeit hat in gleicher oder ähnlicher Form noch
keiner Prüfungsbehörde vorgelegen.

I declare in lieu of oath that I have completed the
present thesis with the above-mentioned title
independently and without any unauthorized
assistance. I have not used any other sources or aids
than the ones listed and have documented quotations
and paraphrases as such. The thesis in its current or
similar version has not been submitted to an auditing
institution before.

Ort, Datum

Unterschrift

(place, date)

(signature)

Belehrung:
Wer vorsätzlich gegen eine die Täuschung über
Prüfungsleistungen betreffende Regelung einer
Hochschulprüfungsordnung
verstößt,
handelt
ordnungswidrig. Die Ordnungswidrigkeit kann mit einer
Geldbuße von bis zu 50.000,00 € geahndet werden.
Zuständige Verwaltungsbehörde für die Verfolgung
und Ahndung von Ordnungswidrigkeiten ist der
Kanzler/die Kanzlerin der Technischen Universität
Dortmund. Im Falle eines mehrfachen oder sonstigen
schwerwiegenden Täuschungsversuches kann der
Prüfling zudem exmatrikuliert werden. (§ 63 Abs. 5
Hochschulgesetz - HG - ).
Die Abgabe einer falschen Versicherung an Eides statt
wird mit Freiheitsstrafe bis zu 3 Jahren oder mit
Geldstrafe bestraft.
Die Technische Universität Dortmund wird ggf.
elektronische Vergleichswerkzeuge (wie z.B. die
Software „turnitin“) zur Überprüfung von Ordnungswidrigkeiten in Prüfungsverfahren nutzen.

Official notification:
Any person who intentionally breaches any regulation
of university examination regulations relating to
deception in examination performance is acting
improperly. This offense can be punished with a fine of
up to EUR 50,000.00. The competent administrative
authority for the pursuit and prosecution of offenses of
this type is the Chancellor of TU Dortmund University.
In the case of multiple or other serious attempts at
deception, the examinee can also be unenrolled,
Section 63 (5) North Rhine-Westphalia Higher
Education Act (Hochschulgesetz, HG).
The submission of a false affidavit will be punished
with a prison sentence of up to three years or a fine.
As may be necessary, TU Dortmund University will
make use of electronic plagiarism-prevention tools
(e.g. the "turnitin" service) in order to monitor violations
during the examination procedures.
I have taken note of the above official notification:*

Die oben stehende Belehrung habe ich zur Kenntnis
genommen:

Ort, Datum

Unterschrift

(place, date)

(signature)

*Please be aware that solely the German version of the affidavit ("Eidesstattliche Versicherung")
for the Bachelor’s/ Master’s thesis is the official and legally binding version.

