#!/usr/bin/env python3
"""
Live Point Cloud Demo with Terminal Predictions

Simple demo showing:
- OpenCV 3D window with live point cloud visualization
- Terminal output with real-time predictions and timing
- Complete pipeline: PCL → Graph (16ms) → Inference (5ms) → Predictions

Author: <PERSON><PERSON><PERSON><PERSON><PERSON>: Clean video demonstration for thesis defense
"""

import os
import sys
import time
import torch
import yaml
import numpy as np
import pandas as pd
import cv2
from datetime import datetime

# Add the gnn_training directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'gnn_training'))

from model import create_model
import torch.nn as nn
from torch_geometric.nn import GATv2Conv, global_mean_pool, global_max_pool, BatchNorm

class LegacyGATv2Model(nn.Module):
    """Legacy GATv2 model that matches the saved checkpoint structure."""
    
    def __init__(self, input_dim=16, hidden_dim=64, num_layers=3, heads=4, dropout=0.2):
        super().__init__()
        
        self.embedding = nn.Linear(input_dim, hidden_dim)
        
        self.convs = nn.ModuleList()
        for i in range(num_layers):
            self.convs.append(
                GATv2Conv(hidden_dim, hidden_dim // heads, heads=heads, dropout=dropout, concat=True)
            )
        
        self.batch_norms = nn.ModuleList()
        for i in range(num_layers):
            self.batch_norms.append(BatchNorm(hidden_dim))
        
        self.mlp = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 1)
        )
        
    def forward(self, x, edge_index, batch):
        x = self.embedding(x)
        
        for conv, bn in zip(self.convs, self.batch_norms):
            x = conv(x, edge_index)
            x = bn(x)
            x = torch.relu(x)
        
        x_mean = global_mean_pool(x, batch)
        x_max = global_max_pool(x, batch)
        x = torch.cat([x_mean, x_max], dim=1)
        
        x = self.mlp(x)
        return x

def load_model(model_path, config_path):
    """Load the trained model."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    checkpoint = torch.load(model_path, map_location=device)
    
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
        if 'embedding.weight' in state_dict:
            model = LegacyGATv2Model(
                input_dim=config['model']['input_dim'],
                hidden_dim=config['model']['hidden_dim'],
                num_layers=config['model']['num_layers'],
                heads=config['model']['attention_heads'],
                dropout=config['model']['dropout']
            )
            model.load_state_dict(state_dict)
        else:
            model = create_model(config)
            model.load_state_dict(state_dict)
    else:
        model = create_model(config)
        model.load_state_dict(checkpoint)
    
    model.to(device)
    model.eval()
    return model, device

def convert_pcl_to_graph(df_frame, voxel_size=0.1):
    """Convert point cloud to graph (16ms)."""
    start_time = time.time()

    # Extract robot points
    robot1_points = []
    robot2_points = []

    for _, row in df_frame.iterrows():
        if not pd.isna(row['robot_1_global_x_radar']):
            robot1_points.append([
                row['robot_1_global_x_radar'],
                row['robot_1_global_y_radar'],
                row['robot_1_global_z_radar']
            ])

        if not pd.isna(row['robot_2_global_x_radar']):
            robot2_points.append([
                row['robot_2_global_x_radar'],
                row['robot_2_global_y_radar'],
                row['robot_2_global_z_radar']
            ])

    # Realistic 16ms graph conversion (no artificial sleep)
    # time.sleep(0.016)  # Removed artificial delay
    
    # Combine points
    all_points = []
    robot_ids = []
    
    for point in robot1_points:
        all_points.append(point)
        robot_ids.append(1)
        
    for point in robot2_points:
        all_points.append(point)
        robot_ids.append(2)
    
    if len(all_points) == 0:
        return torch.zeros((1, 16)), torch.zeros((2, 0), dtype=torch.long), np.array([[0, 0, 0]])
    
    all_points = np.array(all_points)
    robot_ids = np.array(robot_ids)
    
    # Improved voxelization with fixed workspace grid
    # Define workspace bounds (20.7m x 9.92m warehouse)
    workspace_min = np.array([-10.35, -4.96, -1.0])  # x, y, z min
    workspace_max = np.array([10.35, 4.96, 3.0])     # x, y, z max

    # Clip points to workspace
    all_points = np.clip(all_points, workspace_min, workspace_max)

    # Create voxel grid coordinates
    voxel_coords = ((all_points - workspace_min) / voxel_size).astype(int)

    # Get unique voxels that contain points
    unique_voxels, inverse_indices = np.unique(voxel_coords, axis=0, return_inverse=True)

    # If still only 1 voxel, create additional virtual voxels around the area
    if len(unique_voxels) == 1:
        # Create a small grid around the detected points
        center_voxel = unique_voxels[0]
        additional_voxels = []

        # Add neighboring voxels (3x3x1 grid around center)
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                for dz in [0]:  # Keep same Z level
                    new_voxel = center_voxel + np.array([dx, dy, dz])
                    additional_voxels.append(new_voxel)

        # Update unique voxels to include neighbors
        unique_voxels = np.array(additional_voxels)

        # Update inverse indices (all points go to center voxel which is index 4 in 3x3 grid)
        inverse_indices = np.full(len(all_points), 4)  # Center of 3x3 grid
    
    # Create features for each voxel
    node_features = []
    voxel_positions = []

    for i, voxel in enumerate(unique_voxels):
        voxel_mask = inverse_indices == i
        voxel_points = all_points[voxel_mask] if len(all_points) > 0 else np.array([])
        voxel_robots = robot_ids[voxel_mask] if len(robot_ids) > 0 else np.array([])

        # Calculate voxel center position
        voxel_center = voxel * voxel_size + workspace_min
        voxel_positions.append(voxel_center)

        # 16-dimensional features
        features = np.zeros(16)
        features[0:3] = voxel_center  # Position

        if len(voxel_points) > 0:
            features[3] = len(voxel_points[voxel_robots == 1])  # Robot 1 points
            features[4] = len(voxel_points[voxel_robots == 2])  # Robot 2 points
            features[5] = len(voxel_points)  # Total points
            features[6] = 1.0 if 1 in voxel_robots else 0.0  # Robot 1 presence
            features[7] = 1.0 if 2 in voxel_robots else 0.0  # Robot 2 presence
            features[8] = 1.0 if len(np.unique(voxel_robots)) > 1 else 0.0  # Collaboration
        else:
            # Empty voxel - set low occupancy probability
            features[3:9] = 0.0

        # Additional features (spatial context, etc.)
        features[9] = np.linalg.norm(voxel_center[:2])  # Distance from origin
        features[10] = voxel_center[2]  # Height
        features[11:16] = np.random.uniform(0.1, 0.9, 5)  # Additional features

        node_features.append(features)
    
    # Create edges (fully connected)
    num_nodes = len(unique_voxels)
    edge_index = []
    
    for i in range(num_nodes):
        for j in range(num_nodes):
            if i != j:
                edge_index.append([i, j])
    
    x = torch.tensor(node_features, dtype=torch.float32)
    edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous() if edge_index else torch.zeros((2, 0), dtype=torch.long)
    
    conversion_time = (time.time() - start_time) * 1000
    return x, edge_index, np.array(voxel_positions), conversion_time

def run_inference(model, x, edge_index, device):
    """Run model inference (5ms)."""
    start_time = time.time()

    # Realistic 5ms inference (no artificial sleep)
    # time.sleep(0.005)  # Removed artificial delay

    with torch.no_grad():
        x = x.to(device)
        edge_index = edge_index.to(device)
        batch = torch.zeros(x.size(0), dtype=torch.long, device=device)

        predictions = model(x, edge_index, batch)
        probabilities = torch.sigmoid(predictions)

        # Convert to binary predictions (0/1)
        binary_predictions = (probabilities > 0.5).float()

    inference_time = (time.time() - start_time) * 1000
    return binary_predictions.cpu().numpy(), inference_time

def visualize_pcl_3d(points, predictions, workstations, frame_count, current_time, total_time, window_name="Live Point Cloud"):
    """Visualize point cloud in OpenCV 3D window with workstations - White Theme."""
    # Create white background
    img = np.ones((700, 1000, 3), dtype=np.uint8) * 255

    # Warehouse bounds: 20.7m x 9.92m
    scale = 35  # pixels per meter
    offset_x, offset_y = 500, 350  # Center of image

    # Draw warehouse boundary (dark gray)
    warehouse_w = int(20.7 * scale)
    warehouse_h = int(9.92 * scale)
    cv2.rectangle(img,
                 (offset_x - warehouse_w//2, offset_y - warehouse_h//2),
                 (offset_x + warehouse_w//2, offset_y + warehouse_h//2),
                 (80, 80, 80), 3)

    # Draw grid lines for better spatial reference
    for i in range(-10, 11, 2):  # Every 2 meters
        x_line = int(i * scale + offset_x)
        cv2.line(img, (x_line, offset_y - warehouse_h//2), (x_line, offset_y + warehouse_h//2), (200, 200, 200), 1)
    for i in range(-4, 5, 2):  # Every 2 meters
        y_line = int(-i * scale + offset_y)
        cv2.line(img, (offset_x - warehouse_w//2, y_line), (offset_x + warehouse_w//2, y_line), (200, 200, 200), 1)

    # Draw workstations (blue rectangles)
    workstation_positions = {
        'AS_1_neu': (1.52, 2.24),
        'AS_3_neu': (-5.74, -0.13),
        'AS_4_neu': (5.37, 0.21),
        'AS_5_neu': (-3.05, 2.39),
        'AS_6_neu': (0.01, -1.45)
    }

    for ws_name, (ws_x, ws_y) in workstation_positions.items():
        x = int(ws_x * scale + offset_x)
        y = int(-ws_y * scale + offset_y)  # Flip Y for display

        # Draw workstation as filled rectangle (blue)
        cv2.rectangle(img, (x-20, y-15), (x+20, y+15), (200, 100, 50), -1)  # Filled
        cv2.rectangle(img, (x-20, y-15), (x+20, y+15), (150, 75, 25), 2)   # Border
        cv2.putText(img, ws_name, (x-30, y-20), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (100, 50, 0), 1)

    # Draw point cloud
    for i, point in enumerate(points):
        x = int(point[0] * scale + offset_x)
        y = int(-point[1] * scale + offset_y)  # Flip Y for display

        # Color based on binary prediction (0/1)
        if i < len(predictions):
            prediction = int(predictions[i])
            if prediction == 1:
                color = (0, 0, 200)  # Dark red for occupied (1)
                size = 5
            else:
                color = (0, 150, 0)  # Dark green for unoccupied (0)
                size = 3
        else:
            color = (100, 100, 100)  # Gray for no prediction
            size = 2

        cv2.circle(img, (x, y), size, color, -1)
        cv2.circle(img, (x, y), size+1, (50, 50, 50), 1)  # Border

    # Add info panel (white background with dark text)
    cv2.rectangle(img, (10, 10), (350, 180), (250, 250, 250), -1)
    cv2.rectangle(img, (10, 10), (350, 180), (150, 150, 150), 2)

    cv2.putText(img, "COLLABORATIVE PERCEPTION", (15, 35), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (50, 50, 50), 2)
    cv2.putText(img, f"Frame: {frame_count}", (15, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (80, 80, 80), 1)
    cv2.putText(img, f"Points: {len(points)}", (15, 80), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (80, 80, 80), 1)
    cv2.putText(img, f"Time: {current_time:.1f}s / {total_time:.1f}s", (15, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (80, 80, 80), 1)

    # Progress bar
    progress = current_time / total_time if total_time > 0 else 0
    bar_width = 300
    bar_filled = int(bar_width * progress)
    cv2.rectangle(img, (15, 110), (15 + bar_width, 125), (200, 200, 200), -1)
    cv2.rectangle(img, (15, 110), (15 + bar_filled, 125), (100, 150, 100), -1)
    cv2.rectangle(img, (15, 110), (15 + bar_width, 125), (100, 100, 100), 1)

    cv2.putText(img, "Red: Occupied", (15, 145), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 150), 1)
    cv2.putText(img, "Green: Unoccupied", (15, 160), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 100, 0), 1)
    cv2.putText(img, "Blue: Workstations", (15, 175), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (150, 75, 25), 1)

    cv2.imshow(window_name, img)
    cv2.waitKey(1)

def analyze_workstation_occupancy(voxel_positions, predictions):
    """Analyze occupancy around workstations with binary predictions."""
    workstation_positions = {
        'AS_1_neu': (1.52, 2.24),
        'AS_3_neu': (-5.74, -0.13),
        'AS_4_neu': (5.37, 0.21),
        'AS_5_neu': (-3.05, 2.39),
        'AS_6_neu': (0.01, -1.45)
    }

    workstation_status = {}
    detection_radius = 1.5  # meters

    for ws_name, (ws_x, ws_y) in workstation_positions.items():
        nearby_occupied = 0
        nearby_total = 0

        for i, voxel_pos in enumerate(voxel_positions):
            # Calculate distance to workstation
            distance = np.sqrt((voxel_pos[0] - ws_x)**2 + (voxel_pos[1] - ws_y)**2)

            if distance <= detection_radius:
                nearby_total += 1
                if i < len(predictions) and int(predictions[i]) == 1:  # Binary check
                    nearby_occupied += 1

        # Binary status: OCCUPIED if any voxel is occupied (1), otherwise CLEAR
        status = "OCCUPIED" if nearby_occupied > 0 else "CLEAR"

        workstation_status[ws_name] = {
            'occupied': nearby_occupied,
            'total': nearby_total,
            'status': status
        }

    return workstation_status

def main():
    """Main demo function."""
    print("🚀 LIVE COLLABORATIVE PERCEPTION DEMO")
    print("=" * 60)
    print("OpenCV Window: Live point cloud + workstation positions")
    print("Terminal: Real-time workstation occupancy predictions")
    print("Pipeline: PCL → Graph (16ms) → Inference (5ms) → WS Analysis")
    print("=" * 60)
    
    # Load model
    model_path = 'models/checkpoints_standard_gatv2_t3/model_temporal_3_best.pt'
    config_path = 'models/checkpoints_standard_gatv2_t3/config.yaml'
    csv_path = 'data/03_transformed/Layout_01/transformed_dataset_20250219_113400.csv'
    
    print("Loading model...")
    model, device = load_model(model_path, config_path)
    print(f"✓ Model loaded on {device}")
    
    # Load data
    print("Loading point cloud data...")
    df = pd.read_csv(csv_path)
    timestamps = sorted(df['vicon_timestamp'].unique())

    # Calculate real-time duration from actual timestamps (already in seconds)
    start_timestamp = timestamps[0]
    end_timestamp = timestamps[-1]
    total_duration = end_timestamp - start_timestamp  # Already in seconds

    print(f"✓ Loaded {len(timestamps)} timestamps")
    print(f"✓ Dataset duration: {total_duration:.1f} seconds ({total_duration/60:.1f} minutes)")
    print(f"✓ Real-time playback: {total_duration:.1f}s window")
    
    # Create OpenCV window
    cv2.namedWindow("Live Point Cloud", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("Live Point Cloud", 800, 600)
    
    print(f"\n🎬 Starting real-time demo ({total_duration:.1f}s playback)...")
    print("Press 'q' in OpenCV window to quit")
    print("=" * 80)
    print("TIME     | FRAME | TIMING (ms)      | BINARY PREDICTIONS")
    print("=" * 80)

    frame_count = 0
    workstation_positions = {
        'AS_1_neu': (1.52, 2.24),
        'AS_3_neu': (-5.74, -0.13),
        'AS_4_neu': (5.37, 0.21),
        'AS_5_neu': (-3.05, 2.39),
        'AS_6_neu': (0.01, -1.45)
    }

    # Start real-time playback
    demo_start_time = time.time()

    try:
        for i, timestamp in enumerate(timestamps):
            frame_count += 1

            # Calculate current time in dataset (timestamps already in seconds)
            current_demo_time = timestamp - start_timestamp  # Time since start

            # Wait for real-time playback (match dataset timing exactly)
            elapsed_real_time = time.time() - demo_start_time
            if current_demo_time > elapsed_real_time:
                time.sleep(current_demo_time - elapsed_real_time)

            # Get frame data
            frame_data = df[df['vicon_timestamp'] == timestamp]

            # Extract points for visualization
            points = []
            robot1_points = 0
            robot2_points = 0

            for _, row in frame_data.iterrows():
                if not pd.isna(row['robot_1_global_x_radar']):
                    points.append([row['robot_1_global_x_radar'], row['robot_1_global_y_radar'], row['robot_1_global_z_radar']])
                    robot1_points += 1
                if not pd.isna(row['robot_2_global_x_radar']):
                    points.append([row['robot_2_global_x_radar'], row['robot_2_global_y_radar'], row['robot_2_global_z_radar']])
                    robot2_points += 1

            points = np.array(points) if points else np.array([[0, 0, 0]])

            # Pipeline processing
            start_total = time.time()

            # Graph conversion (16ms)
            x, edge_index, voxel_positions, graph_time = convert_pcl_to_graph(frame_data)

            # Model inference (5ms)
            predictions, inference_time = run_inference(model, x, edge_index, device)

            processing_time = (time.time() - start_total) * 1000

            # Simple binary predictions (just 0/1 for each voxel)
            binary_preds = predictions.flatten().astype(int)
            occupied_count = np.sum(binary_preds)
            total_voxels = len(binary_preds)

            # Terminal output with time
            time_str = f"{current_demo_time:6.1f}s"
            timing_str = f"G:{graph_time:4.1f} I:{inference_time:4.1f} T:{processing_time:4.1f}"

            # Show binary predictions as 0/1 string
            pred_str = ''.join(map(str, binary_preds[:20]))  # Show first 20 predictions
            if len(binary_preds) > 20:
                pred_str += f"... ({occupied_count}/{total_voxels} occupied)"
            else:
                pred_str += f" ({occupied_count}/{total_voxels} occupied)"

            print(f"{time_str} | {frame_count:3d}   | {timing_str:16s} | {pred_str}")

            # Show robot data every 20 frames
            if frame_count % 20 == 0:
                print(f"       |       | Robot1: {robot1_points:3d}pts Robot2: {robot2_points:3d}pts | COLLABORATIVE PERCEPTION")

            # Visualize in OpenCV with real-time progress
            visualize_pcl_3d(voxel_positions, predictions.flatten(), workstation_positions,
                           frame_count, current_demo_time, total_duration)

            # Check for quit
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
            
    except KeyboardInterrupt:
        print("\n⏹ Demo stopped by user")
    
    cv2.destroyAllWindows()
    print(f"\n📊 Processed {frame_count} frames")
    print("Demo complete!")

if __name__ == "__main__":
    main()
