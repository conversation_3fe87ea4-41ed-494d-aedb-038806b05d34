# Collaborative Perception Management Layer (CPML)

A comprehensive framework for collaborative perception in warehouse robotics using Graph Neural Networks (GNNs) and multi-robot sensor fusion.

## 🎯 Project Overview

This project implements a **Collaborative Perception Management Layer** for future 6G-enabled robotic systems in warehouse environments. The framework processes mmWave radar and Vicon motion capture data from multiple robots to create graph-structured datasets for training collaborative perception models.

### Key Features
- **Multi-robot sensor data processing** (mmWave radar + Vicon motion capture)
- **Temporal data synchronization** across heterogeneous sensors
- **Coordinate transformation** from robot-local to global reference frames
- **Graph Neural Network data preparation** for collaborative perception training
- **Interactive visualization tools** for data exploration and validation
- **Comprehensive preprocessing pipeline** with quality assurance

## 📁 Repository Structure

```
CPML/
├── gnn_training/           # GNN model training and evaluation
├── preprocess scripts/     # Data preprocessing pipeline
├── models/                 # Trained model checkpoints and configs
└── README.md              # This file
```

## 🔧 Preprocessing Scripts

### Core Data Processing Pipeline

#### `extract_radar_data.py`
**Purpose**: Extracts and processes mmWave radar point cloud data from ROS bag files
- Converts ROS bag radar data to structured CSV format
- Handles multiple robot radar streams (Robot_1: ep03, Robot_2: ep05)
- Extracts timestamps, spatial coordinates, and radar-specific features
- Supports batch processing of multiple scenarios

**Usage**: Essential first step for processing raw radar sensor data

#### `extract_vicon_data.py`
**Purpose**: Processes high-precision Vicon motion capture data
- Extracts 6DOF pose data (position + orientation) for robots and objects
- Converts Vicon coordinate system to warehouse global coordinates
- Handles workstation positions and robot trajectories
- Provides ground truth data for validation and training

**Usage**: Processes motion capture data for precise spatial reference

#### `multi_robot_data_synchronizer.py`
**Purpose**: Synchronizes multi-robot sensor data across time
- Temporally aligns radar data from multiple robots with Vicon data
- Handles different sampling rates and timing offsets
- Implements interpolation for smooth temporal alignment
- Creates synchronized datasets ready for collaborative processing

**Usage**: Critical for creating temporally consistent multi-robot datasets

### Coordinate Transformation

#### `coordinate_transformation.py`
**Purpose**: Transforms coordinates between reference frames
- Converts robot-local radar coordinates to global warehouse coordinates
- Uses Vicon pose data for precise transformation matrices
- Handles rotation and translation transformations
- Ensures spatial consistency across multi-robot data

**Usage**: Essential for creating globally consistent collaborative datasets


### Data Quality and Annotation

#### `point_cloud_cleaner.py`
**Purpose**: Cleans and filters point cloud data for quality assurance
- Removes outliers and noise from radar measurements
- Applies spatial, temporal, and signal quality filters
- Handles boundary constraints for warehouse environment
- Optimizes data quality for downstream processing

**Usage**: Ensures high-quality input data for graph generation

#### `point_cloud_annotator.py`
**Purpose**: Automatically annotates point cloud data with semantic labels
- Identifies and labels workstations, robots, and boundaries
- Creates ground truth annotations for supervised learning
- Supports batch annotation of large datasets
- Provides visualization for annotation validation

**Usage**: Creates labeled datasets for supervised collaborative perception training

### Graph Data Generation

#### `point_cloud_to_graph_converter.py`
**Purpose**: Converts synchronized point cloud data to graph structures for GNN training
- Creates graph nodes from radar point cloud data
- Generates edges based on spatial proximity and collaborative relationships
- Adds node features (position, occupancy, robot contributions)
- Outputs PyTorch Geometric compatible graph data

**Usage**: Final step to create GNN-ready training data



### Utility Scripts


#### `split_frames.py`
**Purpose**: Creates balanced train/validation/test splits for GNN training
- Maintains temporal integrity across dataset splits
- Ensures balanced representation across warehouse layouts
- Supports multiple temporal window configurations
- Creates stratified splits for robust model training

**Usage**: Prepares balanced datasets for GNN model training and evaluation


## 🧠 GNN Training Module

The `gnn_training/` directory contains the Graph Neural Network training pipeline:

- **`main.py`**: Main training script with experiment management
- **`model.py`**: GNN model architectures (GAT, ECC)
- **`train.py`**: Training loop implementation
- **`evaluate.py`**: Model evaluation and metrics
- **`data.py`**: Data loading and preprocessing for training
- **`utils.py`**: Utility functions for training pipeline
- **`ablation.py`**: Ablation study framework
- **`class_weight_config.py`**: Class balancing configuration

