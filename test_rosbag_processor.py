#!/usr/bin/env python3
"""
Test Script for Live ROS Bag Processor

This script provides a simplified test interface for the live ROS bag processor,
allowing you to test with your existing ROS bag data.

Usage:
    python test_rosbag_processor.py --rosbag /path/to/rosbag --model models/checkpoints_standard_gatv2_t3/model_temporal_3_best.pt
"""

import os
import sys
import argparse
import subprocess
import time
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are available"""
    print("Checking dependencies...")
    
    # Check ROS2
    try:
        result = subprocess.run(['ros2', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ ROS2 found: {result.stdout.strip()}")
        else:
            print("❌ ROS2 not found")
            return False
    except FileNotFoundError:
        print("❌ ROS2 not found")
        return False
    
    # Check Python packages
    required_packages = [
        'torch', 'torch_geometric', 'numpy', 'pandas', 
        'matplotlib', 'rclpy', 'sensor_msgs'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True

def analyze_rosbag(rosbag_path):
    """Analyze ROS bag contents"""
    print(f"\nAnalyzing ROS bag: {rosbag_path}")
    
    try:
        # Get bag info
        result = subprocess.run(
            ['ros2', 'bag', 'info', rosbag_path],
            capture_output=True, text=True
        )
        
        if result.returncode == 0:
            print("ROS Bag Information:")
            print("-" * 40)
            print(result.stdout)
            return True
        else:
            print(f"Error getting bag info: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"Error analyzing bag: {e}")
        return False

def test_model_loading(model_path, config_path):
    """Test if model can be loaded successfully"""
    print(f"\nTesting model loading...")
    
    try:
        import torch
        import yaml
        
        # Add gnn_training to path
        sys.path.append(os.path.join(os.path.dirname(__file__), 'gnn_training'))
        from model import create_model
        
        # Load config
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Create model
        model = create_model(config)
        
        # Load checkpoint
        checkpoint = torch.load(model_path, map_location='cpu')
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        model.eval()
        
        print(f"✓ Model loaded successfully")
        print(f"  Architecture: {config['model']['gnn_type']}")
        print(f"  Input dim: {config['model']['input_dim']}")
        print(f"  Hidden dim: {config['model']['hidden_dim']}")
        print(f"  Layers: {config['model']['num_layers']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False

def run_simple_test(rosbag_path, model_path, config_path):
    """Run a simple test of the processing pipeline"""
    print(f"\nRunning simple processing test...")
    
    try:
        # Import the processor
        from live_rosbag_processor import ProcessingConfig, extract_rosbag_metadata
        
        # Create config
        config = ProcessingConfig(
            rosbag_path=rosbag_path,
            model_path=model_path,
            config_path=config_path,
            visualization=False  # Disable for testing
        )
        
        # Test metadata extraction
        metadata = extract_rosbag_metadata(rosbag_path)
        print(f"✓ Metadata extraction successful")
        print(f"  Duration: {metadata['duration_seconds']:.1f} seconds")
        print(f"  Topics: {len(metadata['topics'])}")
        
        # Check for required topics
        topic_names = [topic[0] for topic in metadata['topics']]
        radar_topics = [t for t in topic_names if 'radar_scan_pcl' in t]
        vicon_topics = [t for t in topic_names if 'vicon/pose' in t]
        
        print(f"  Radar topics: {len(radar_topics)}")
        print(f"  Vicon topics: {len(vicon_topics)}")
        
        if radar_topics:
            print(f"  Radar topics found: {radar_topics}")
        
        if vicon_topics:
            print(f"  Vicon topics found: {vicon_topics}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in processing test: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(
        description="Test Live ROS Bag Processor",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        '--rosbag', type=str, required=True,
        help='Path to ROS bag directory'
    )
    
    parser.add_argument(
        '--model', type=str, required=True,
        help='Path to trained model checkpoint'
    )
    
    parser.add_argument(
        '--config', type=str, default=None,
        help='Path to model config file (auto-detected if not provided)'
    )
    
    parser.add_argument(
        '--run-live', action='store_true',
        help='Run the live processor after tests pass'
    )
    
    args = parser.parse_args()
    
    print("="*60)
    print("LIVE ROS BAG PROCESSOR - TEST SUITE")
    print("="*60)
    
    # Validate inputs
    if not os.path.exists(args.rosbag):
        print(f"❌ ROS bag path does not exist: {args.rosbag}")
        return 1
    
    if not os.path.exists(args.model):
        print(f"❌ Model file does not exist: {args.model}")
        return 1
    
    # Auto-detect config file
    if args.config is None:
        model_dir = os.path.dirname(args.model)
        config_path = os.path.join(model_dir, 'config.yaml')
        if os.path.exists(config_path):
            args.config = config_path
        else:
            print(f"❌ Could not find config.yaml in {model_dir}")
            return 1
    
    # Run tests
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Dependencies
    if check_dependencies():
        tests_passed += 1
    
    # Test 2: ROS bag analysis
    if analyze_rosbag(args.rosbag):
        tests_passed += 1
    
    # Test 3: Model loading
    if test_model_loading(args.model, args.config):
        tests_passed += 1
    
    # Test 4: Simple processing test
    if run_simple_test(args.rosbag, args.model, args.config):
        tests_passed += 1
    
    # Results
    print("\n" + "="*60)
    print(f"TEST RESULTS: {tests_passed}/{total_tests} PASSED")
    print("="*60)
    
    if tests_passed == total_tests:
        print("✅ All tests passed! Ready for live processing.")
        
        if args.run_live:
            print("\nStarting live processor...")
            time.sleep(2)
            
            # Run the live processor
            cmd = [
                'python', 'live_rosbag_processor.py',
                '--rosbag', args.rosbag,
                '--model', args.model,
                '--config', args.config
            ]
            
            print(f"Executing: {' '.join(cmd)}")
            subprocess.run(cmd)
        else:
            print("\nTo run live processing:")
            print(f"python live_rosbag_processor.py --rosbag {args.rosbag} --model {args.model}")
    
    else:
        print("❌ Some tests failed. Please fix issues before running live processing.")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
