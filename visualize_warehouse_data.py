#!/usr/bin/env python3
"""
Real-time Warehouse Visualization for transformed_dataset_20250219_121000.csv
Shows warehouse arena with workstation positions, robot positions, and point cloud data.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Rectangle
from matplotlib.widgets import Slider, Button
import time
import argparse

class WarehouseVisualizer:
    def __init__(self, csv_file, sample_rate=10):
        print("WAREHOUSE REAL-TIME VISUALIZER")
        print("=" * 50)
        
        # Load data
        print("Loading dataset...")
        self.data = pd.read_csv(csv_file)
        print(f"Loaded {len(self.data)} data points")

        # Sample data for better performance (every 10th frame for real-time visualization)
        self.data = self.data.iloc[::10].reset_index(drop=True)
        print(f"Using {len(self.data)} frames for visualization (sampled for performance)")
        
        # Warehouse dimensions (20.7m x 9.92m from user preferences)
        self.warehouse_bounds = {
            'x_min': -10.35, 'x_max': 10.35,  # 20.7m total
            'y_min': -4.96, 'y_max': 4.96     # 9.92m total
        }
        
        # Layout_01 workstation positions (from user's data location preferences)
        self.workstations = {
            'AS_1': {'x': 1.52, 'y': 2.24, 'color': 'blue'},
            'AS_3': {'x': -5.74, 'y': -0.13, 'color': 'green'},
            'AS_4': {'x': 5.37, 'y': 0.21, 'color': 'red'},
            'AS_5': {'x': -3.05, 'y': 2.39, 'color': 'orange'},
            'AS_6': {'x': 0.01, 'y': -1.45, 'color': 'purple'}
        }
        
        # Animation settings
        self.current_frame = 0
        self.speed = 50  # ms between frames
        self.max_pcl_history = 15
        self.pcl_history = []
        
        self.setup_plot()
        
    def setup_plot(self):
        """Setup matplotlib figure and axes with time slider."""
        self.fig = plt.figure(figsize=(16, 10))

        # Main plot area (leave space at bottom for slider)
        self.ax = plt.subplot2grid((10, 1), (0, 0), rowspan=8)
        self.ax.set_xlim(self.warehouse_bounds['x_min'], self.warehouse_bounds['x_max'])
        self.ax.set_ylim(self.warehouse_bounds['y_min'], self.warehouse_bounds['y_max'])
        self.ax.set_aspect('equal')
        self.ax.grid(True, alpha=0.3)
        self.ax.set_xlabel('X Position (m)', fontsize=12)
        self.ax.set_ylabel('Y Position (m)', fontsize=12)
        self.ax.set_title('Real-time Warehouse Robotics - Layout_01', fontsize=14, fontweight='bold')

        # Time slider area
        slider_ax = plt.subplot2grid((10, 1), (8, 0))
        self.time_slider = Slider(
            slider_ax, 'Time Frame', 0, len(self.data)-1,
            valinit=0, valfmt='%d', valstep=1
        )
        self.time_slider.on_changed(self.on_slider_change)

        # Control buttons area
        button_ax = plt.subplot2grid((10, 1), (9, 0))
        button_ax.axis('off')

        # Play/Pause button
        play_ax = plt.axes([0.1, 0.02, 0.1, 0.04])
        self.play_button = Button(play_ax, 'Play/Pause')
        self.play_button.on_clicked(self.toggle_play_pause)

        # Reset button
        reset_ax = plt.axes([0.25, 0.02, 0.1, 0.04])
        self.reset_button = Button(reset_ax, 'Reset')
        self.reset_button.on_clicked(self.reset_animation)

        # Animation control
        self.is_playing = True
        self._updating_from_slider = False
        
        # Draw warehouse boundary
        boundary = Rectangle(
            (self.warehouse_bounds['x_min'], self.warehouse_bounds['y_min']),
            20.7, 9.92,
            linewidth=3, edgecolor='black', facecolor='none', linestyle='--'
        )
        self.ax.add_patch(boundary)
        
        # Draw workstations
        for ws_name, ws_data in self.workstations.items():
            ws_rect = Rectangle(
                (ws_data['x'] - 0.4, ws_data['y'] - 0.4), 0.8, 0.8,
                facecolor=ws_data['color'], alpha=0.8, edgecolor='black', linewidth=2
            )
            self.ax.add_patch(ws_rect)
            self.ax.text(ws_data['x'], ws_data['y'], ws_name, 
                        ha='center', va='center', fontweight='bold', fontsize=9, color='white')
        
        # Initialize plots
        self.robot1_plot, = self.ax.plot([], [], 'ro', markersize=14, label='Robot 1 (ep03)', 
                                        markeredgecolor='darkred', markeredgewidth=2, zorder=5)
        self.robot2_plot, = self.ax.plot([], [], 'bo', markersize=14, label='Robot 2 (ep05)', 
                                        markeredgecolor='darkblue', markeredgewidth=2, zorder=5)
        
        # Point cloud plots
        self.pcl1_plot, = self.ax.plot([], [], 'r.', markersize=3, alpha=0.7, label='Robot 1 PCL')
        self.pcl2_plot, = self.ax.plot([], [], 'b.', markersize=3, alpha=0.7, label='Robot 2 PCL')
        
        # Robot trajectories
        self.traj1_plot, = self.ax.plot([], [], 'r-', alpha=0.4, linewidth=1.5, label='Robot 1 Path')
        self.traj2_plot, = self.ax.plot([], [], 'b-', alpha=0.4, linewidth=1.5, label='Robot 2 Path')
        
        # Info display
        self.info_text = self.ax.text(0.02, 0.98, '', transform=self.ax.transAxes, 
                                     verticalalignment='top', fontsize=10,
                                     bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.9))
        
        # Legend
        self.ax.legend(loc='upper right', bbox_to_anchor=(1.15, 1), fontsize=9)
        
        # Trajectory storage
        self.robot1_traj = []
        self.robot2_traj = []
        self.max_traj = 50
        
    def get_frame_data(self, frame_idx):
        """Extract data for current frame using correct global coordinates."""
        if frame_idx >= len(self.data):
            return None

        row = self.data.iloc[frame_idx]

        # Use global coordinates for robot positions
        robot1_pos = (row['robot_1_global_x'], row['robot_1_global_y'])
        robot2_pos = (row['robot_2_global_x'], row['robot_2_global_y'])

        # Use global radar coordinates for point cloud positions
        pcl1_pos = (row['robot_1_global_x_radar'], row['robot_1_global_y_radar'])
        pcl2_pos = (row['robot_2_global_x_radar'], row['robot_2_global_y_radar'])

        return {
            'robot1_pos': robot1_pos,
            'robot2_pos': robot2_pos,
            'pcl1_pos': pcl1_pos,
            'pcl2_pos': pcl2_pos,
            'timestamp': row['vicon_timestamp'],
            'frame_idx': frame_idx
        }

    def on_slider_change(self, val):
        """Handle slider value change."""
        self.current_frame = int(val)
        self.update_visualization_no_slider()

    def toggle_play_pause(self, event):
        """Toggle play/pause state."""
        self.is_playing = not self.is_playing
        if self.is_playing:
            if hasattr(self, 'anim'):
                self.anim.resume()
        else:
            if hasattr(self, 'anim'):
                self.anim.pause()

    def reset_animation(self, event):
        """Reset animation to beginning."""
        self.current_frame = 0
        self.time_slider.reset()
        self.pcl_history = []
        self.robot1_traj = []
        self.robot2_traj = []
        self.update_visualization()
    
    def update_visualization(self):
        """Update the visualization with current frame data."""
        frame_data = self.get_frame_data(self.current_frame)

        if frame_data is None:
            return

        # Update robot positions
        self.robot1_plot.set_data([frame_data['robot1_pos'][0]], [frame_data['robot1_pos'][1]])
        self.robot2_plot.set_data([frame_data['robot2_pos'][0]], [frame_data['robot2_pos'][1]])

        # Update trajectories
        self.robot1_traj.append(frame_data['robot1_pos'])
        self.robot2_traj.append(frame_data['robot2_pos'])

        if len(self.robot1_traj) > self.max_traj:
            self.robot1_traj.pop(0)
        if len(self.robot2_traj) > self.max_traj:
            self.robot2_traj.pop(0)

        # Plot trajectories
        if len(self.robot1_traj) > 1:
            traj1_x, traj1_y = zip(*self.robot1_traj)
            self.traj1_plot.set_data(traj1_x, traj1_y)

        if len(self.robot2_traj) > 1:
            traj2_x, traj2_y = zip(*self.robot2_traj)
            self.traj2_plot.set_data(traj2_x, traj2_y)

        # Update point clouds
        self.pcl_history.append({
            'pcl1': frame_data['pcl1_pos'],
            'pcl2': frame_data['pcl2_pos']
        })

        if len(self.pcl_history) > self.max_pcl_history:
            self.pcl_history.pop(0)

        # Collect point cloud data
        pcl1_x, pcl1_y = [], []
        pcl2_x, pcl2_y = [], []

        for pcl_data in self.pcl_history:
            # Robot 1 PCL
            pcl1_x_val, pcl1_y_val = pcl_data['pcl1']
            if (not np.isnan(pcl1_x_val) and not np.isnan(pcl1_y_val) and
                abs(pcl1_x_val) < 50 and abs(pcl1_y_val) < 50):
                if (self.warehouse_bounds['x_min'] <= pcl1_x_val <= self.warehouse_bounds['x_max'] and
                    self.warehouse_bounds['y_min'] <= pcl1_y_val <= self.warehouse_bounds['y_max']):
                    pcl1_x.append(pcl1_x_val)
                    pcl1_y.append(pcl1_y_val)

            # Robot 2 PCL
            pcl2_x_val, pcl2_y_val = pcl_data['pcl2']
            if (not np.isnan(pcl2_x_val) and not np.isnan(pcl2_y_val) and
                abs(pcl2_x_val) < 50 and abs(pcl2_y_val) < 50):
                if (self.warehouse_bounds['x_min'] <= pcl2_x_val <= self.warehouse_bounds['x_max'] and
                    self.warehouse_bounds['y_min'] <= pcl2_y_val <= self.warehouse_bounds['y_max']):
                    pcl2_x.append(pcl2_x_val)
                    pcl2_y.append(pcl2_y_val)

        self.pcl1_plot.set_data(pcl1_x, pcl1_y)
        self.pcl2_plot.set_data(pcl2_x, pcl2_y)

        # Update info text
        timestamp_str = time.strftime('%H:%M:%S', time.localtime(frame_data['timestamp']))
        progress = (frame_data['frame_idx'] / len(self.data)) * 100

        info_str = f"Frame: {frame_data['frame_idx']}/{len(self.data)} ({progress:.1f}%)\n"
        info_str += f"Time: {timestamp_str}\n"
        info_str += f"Robot 1: ({frame_data['robot1_pos'][0]:.2f}, {frame_data['robot1_pos'][1]:.2f})\n"
        info_str += f"Robot 2: ({frame_data['robot2_pos'][0]:.2f}, {frame_data['robot2_pos'][1]:.2f})\n"
        info_str += f"PCL Points: R1({len(pcl1_x)}), R2({len(pcl2_x)})"

        self.info_text.set_text(info_str)

        # Update slider position (only if not called from slider)
        if not self._updating_from_slider:
            self.time_slider.set_val(self.current_frame)

        # Redraw
        self.fig.canvas.draw()

    def update_visualization_no_slider(self):
        """Update visualization without updating slider (to avoid recursion)."""
        self._updating_from_slider = True
        self.update_visualization()
        self._updating_from_slider = False

    def update_animation(self, frame_num):
        """Update animation frame (for automatic playback)."""
        if not self.is_playing:
            return self.robot1_plot, self.robot2_plot, self.pcl1_plot, self.pcl2_plot, self.traj1_plot, self.traj2_plot, self.info_text

        if self.current_frame >= len(self.data):
            # Loop back to start
            self.current_frame = 0
            self.pcl_history = []
            self.robot1_traj = []
            self.robot2_traj = []

        self.update_visualization()

        # Advance frame only if playing
        if self.is_playing:
            self.current_frame += 1
        
        return self.robot1_plot, self.robot2_plot, self.pcl1_plot, self.pcl2_plot, self.traj1_plot, self.traj2_plot, self.info_text
    
    def start_visualization(self):
        """Start the real-time visualization."""
        print("Starting visualization...")
        print(f"Dataset: {len(self.data)} frames")
        print(f"Duration: {(self.data.iloc[-1]['vicon_timestamp'] - self.data.iloc[0]['vicon_timestamp']):.1f} seconds")

        # Print sample coordinates for verification
        sample_frame = self.get_frame_data(0)
        if sample_frame:
            print(f"Sample Robot 1 position: {sample_frame['robot1_pos']}")
            print(f"Sample Robot 2 position: {sample_frame['robot2_pos']}")
            print(f"Sample Robot 1 PCL: {sample_frame['pcl1_pos']}")
            print(f"Sample Robot 2 PCL: {sample_frame['pcl2_pos']}")

        print("Controls:")
        print("- Use time slider to scrub through data")
        print("- Play/Pause button to control playback")
        print("- Reset button to go back to start")
        print("- Press Ctrl+C to stop")

        # Initialize with first frame
        self.update_visualization()

        # Create animation
        self.anim = animation.FuncAnimation(
            self.fig, self.update_animation,
            interval=self.speed,
            blit=True,
            repeat=True,
            cache_frame_data=False
        )

        plt.tight_layout()
        plt.show()

def main():
    parser = argparse.ArgumentParser(description='Warehouse Real-time Visualizer')
    parser.add_argument('--csv', type=str, 
                       default='data/03_transformed/Layout_01/transformed_dataset_20250219_121000.csv',
                       help='Path to CSV file')
    parser.add_argument('--speed', type=int, default=50,
                       help='Animation speed in ms (default: 50)')
    
    args = parser.parse_args()
    
    try:
        visualizer = WarehouseVisualizer(args.csv)
        visualizer.speed = args.speed
        visualizer.start_visualization()
    except KeyboardInterrupt:
        print("\nVisualization stopped")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
