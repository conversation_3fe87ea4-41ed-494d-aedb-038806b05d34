#!/usr/bin/env python3
"""
Setup Script for Live ROS Bag Processor

This script helps set up the environment and dependencies for the live ROS bag processor.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} detected. Python 3.8+ required.")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro}")
    return True

def check_ros2():
    """Check if ROS2 is installed and sourced"""
    try:
        result = subprocess.run(['ros2', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ ROS2 found: {result.stdout.strip()}")
            return True
        else:
            print("❌ ROS2 not found or not sourced")
            return False
    except FileNotFoundError:
        print("❌ ROS2 not found. Please install ROS2 and source the setup script.")
        print("   Ubuntu: sudo apt install ros-humble-desktop")
        print("   Source: source /opt/ros/humble/setup.bash")
        return False

def install_python_packages():
    """Install required Python packages"""
    packages = [
        'torch',
        'torch-geometric', 
        'numpy',
        'pandas',
        'matplotlib',
        'pyyaml',
        'scikit-learn'
    ]
    
    print("Installing Python packages...")
    for package in packages:
        try:
            print(f"Installing {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                         check=True, capture_output=True)
            print(f"✓ {package}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    return True

def install_ros2_packages():
    """Install required ROS2 packages"""
    packages = [
        'ros-humble-sensor-msgs-py',
        'ros-humble-geometry-msgs',
        'ros-humble-nav-msgs',
        'ros-humble-rosbag2-py'
    ]
    
    print("Installing ROS2 packages...")
    for package in packages:
        try:
            print(f"Installing {package}...")
            subprocess.run(['sudo', 'apt', 'install', '-y', package], 
                         check=True, capture_output=True)
            print(f"✓ {package}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    return True

def check_models():
    """Check if trained models are available"""
    models_dir = Path('models')
    if not models_dir.exists():
        print("❌ Models directory not found")
        return False
    
    # Check for model checkpoints
    model_dirs = [
        'checkpoints_standard_gatv2_t3',
        'checkpoints_standard_gatv2_t5',
        'checkpoints_ecc_t3',
        'checkpoints_ecc_t5'
    ]
    
    available_models = []
    for model_dir in model_dirs:
        model_path = models_dir / model_dir
        if model_path.exists():
            config_file = model_path / 'config.yaml'
            model_file = list(model_path.glob('*.pt'))
            
            if config_file.exists() and model_file:
                available_models.append(model_dir)
                print(f"✓ {model_dir}")
            else:
                print(f"⚠️  {model_dir} (incomplete)")
    
    if available_models:
        print(f"Found {len(available_models)} complete model(s)")
        return True
    else:
        print("❌ No complete models found")
        return False

def check_preprocessing_scripts():
    """Check if preprocessing scripts are available"""
    preprocess_dir = Path('preprocess scripts')
    if not preprocess_dir.exists():
        print("❌ Preprocessing scripts directory not found")
        return False
    
    required_scripts = [
        'coordinate_transformation.py',
        'point_cloud_cleaner.py',
        'point_cloud_to_graph_converter.py',
        'multi_robot_data_synchronizer.py'
    ]
    
    missing_scripts = []
    for script in required_scripts:
        script_path = preprocess_dir / script
        if script_path.exists():
            print(f"✓ {script}")
        else:
            print(f"❌ {script}")
            missing_scripts.append(script)
    
    return len(missing_scripts) == 0

def check_gnn_training():
    """Check if GNN training modules are available"""
    gnn_dir = Path('gnn_training')
    if not gnn_dir.exists():
        print("❌ GNN training directory not found")
        return False
    
    required_modules = [
        'model.py',
        'data.py',
        'train.py',
        'evaluate.py'
    ]
    
    missing_modules = []
    for module in required_modules:
        module_path = gnn_dir / module
        if module_path.exists():
            print(f"✓ {module}")
        else:
            print(f"❌ {module}")
            missing_modules.append(module)
    
    return len(missing_modules) == 0

def create_example_config():
    """Create an example configuration file"""
    config_content = """# Example configuration for live ROS bag processing

# ROS bag settings
rosbag:
  path: "/path/to/your/rosbag"
  playback_rate: 1.0

# Model settings
model:
  checkpoint: "models/checkpoints_standard_gatv2_t3/model_temporal_3_best.pt"
  config: "models/checkpoints_standard_gatv2_t3/config.yaml"

# Processing settings
processing:
  voxel_size: 0.1
  temporal_window: 3
  max_latency_ms: 25.0
  arena_bounds: [0, 20.7, 0, 9.92]  # x_min, x_max, y_min, y_max

# Visualization settings
visualization:
  enabled: true
  update_rate: 10  # Hz

# Performance settings
performance:
  cleanup_interval: 5.0  # seconds
  buffer_size: 100  # frames
"""
    
    config_path = Path('live_processor_config.yaml')
    with open(config_path, 'w') as f:
        f.write(config_content)
    
    print(f"✓ Created example config: {config_path}")

def main():
    print("="*60)
    print("LIVE ROS BAG PROCESSOR - SETUP")
    print("="*60)
    
    setup_success = True
    
    # Check Python version
    print("\n1. Checking Python version...")
    if not check_python_version():
        setup_success = False
    
    # Check ROS2
    print("\n2. Checking ROS2...")
    if not check_ros2():
        setup_success = False
    
    # Check project structure
    print("\n3. Checking project structure...")
    print("Models:")
    if not check_models():
        setup_success = False
    
    print("\nPreprocessing scripts:")
    if not check_preprocessing_scripts():
        setup_success = False
    
    print("\nGNN training modules:")
    if not check_gnn_training():
        setup_success = False
    
    # Install packages if everything looks good
    if setup_success:
        print("\n4. Installing dependencies...")
        
        # Install Python packages
        if not install_python_packages():
            setup_success = False
        
        # Install ROS2 packages (optional, may require sudo)
        try:
            if not install_ros2_packages():
                print("⚠️  Some ROS2 packages failed to install (may require manual installation)")
        except Exception as e:
            print(f"⚠️  ROS2 package installation skipped: {e}")
    
    # Create example config
    print("\n5. Creating example configuration...")
    create_example_config()
    
    # Final status
    print("\n" + "="*60)
    if setup_success:
        print("✅ SETUP COMPLETE!")
        print("="*60)
        print("\nNext steps:")
        print("1. Edit live_processor_config.yaml with your settings")
        print("2. Test with: python test_rosbag_processor.py --rosbag /path/to/rosbag --model /path/to/model")
        print("3. Run live: python live_rosbag_processor.py --rosbag /path/to/rosbag --model /path/to/model")
    else:
        print("❌ SETUP INCOMPLETE")
        print("="*60)
        print("\nPlease fix the issues above before proceeding.")
    
    return 0 if setup_success else 1

if __name__ == '__main__':
    exit(main())
