#!/usr/bin/env python3
"""
Real-time Warehouse Robotics Visualization
Displays warehouse arena with workstation positions, robot positions, and point cloud data in real-time.
Created from scratch for Layout_01 visualization.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Rectangle, Circle
import time
import argparse
import sys

class WarehouseRealtimeVisualizer:
    def __init__(self, csv_file):
        """Initialize the warehouse visualizer."""
        print("🏭 WAREHOUSE REAL-TIME VISUALIZER")
        print("=" * 50)
        
        # Load data
        print("Loading dataset...")
        self.data = pd.read_csv(csv_file)
        print(f"✓ Loaded {len(self.data)} data points")
        
        # Warehouse dimensions (20.7m x 9.92m)
        self.warehouse_bounds = {
            'x_min': -10.35, 'x_max': 10.35,  # 20.7m total width
            'y_min': -4.96, 'y_max': 4.96     # 9.92m total height
        }
        
        # Workstation positions from Layout_01 (from existing codebase analysis)
        self.workstations = {
            'AS_1': {'x': 1.52, 'y': 2.24, 'color': 'blue'},
            'AS_3': {'x': -5.74, 'y': -0.13, 'color': 'green'},
            'AS_4': {'x': 5.37, 'y': 0.21, 'color': 'red'},
            'AS_5': {'x': -3.05, 'y': 2.39, 'color': 'orange'},
            'AS_6': {'x': 0.01, 'y': -1.45, 'color': 'purple'}
        }
        
        # Animation parameters
        self.current_frame = 0
        self.playback_speed = 50  # milliseconds between frames
        self.point_cloud_history = []  # Store recent point clouds for trails
        self.max_history = 20  # Increased for better trails
        self.paused = False

        # Robot trajectory tracking
        self.robot1_trajectory = []
        self.robot2_trajectory = []
        self.max_trajectory = 100  # Keep last 100 positions

        # Setup matplotlib
        self.setup_plot()
        
    def setup_plot(self):
        """Setup the matplotlib figure and axes."""
        self.fig, self.ax = plt.subplots(figsize=(16, 10))
        self.ax.set_xlim(self.warehouse_bounds['x_min'], self.warehouse_bounds['x_max'])
        self.ax.set_ylim(self.warehouse_bounds['y_min'], self.warehouse_bounds['y_max'])
        self.ax.set_aspect('equal')
        self.ax.grid(True, alpha=0.3)
        self.ax.set_xlabel('X Position (m)', fontsize=12)
        self.ax.set_ylabel('Y Position (m)', fontsize=12)
        self.ax.set_title('Real-time Warehouse Robotics Visualization - Layout_01', fontsize=14, fontweight='bold')
        
        # Draw warehouse boundary
        warehouse_rect = Rectangle(
            (self.warehouse_bounds['x_min'], self.warehouse_bounds['y_min']),
            self.warehouse_bounds['x_max'] - self.warehouse_bounds['x_min'],
            self.warehouse_bounds['y_max'] - self.warehouse_bounds['y_min'],
            linewidth=3, edgecolor='black', facecolor='none', linestyle='--'
        )
        self.ax.add_patch(warehouse_rect)
        
        # Draw workstations
        self.draw_workstations()
        
        # Initialize robot and point cloud plots
        self.robot1_plot, = self.ax.plot([], [], 'ro', markersize=15, label='Robot 1 (ep03)', zorder=5, markeredgecolor='darkred', markeredgewidth=2)
        self.robot2_plot, = self.ax.plot([], [], 'bo', markersize=15, label='Robot 2 (ep05)', zorder=5, markeredgecolor='darkblue', markeredgewidth=2)

        # Robot trajectory plots
        self.robot1_traj_plot, = self.ax.plot([], [], 'r-', alpha=0.5, linewidth=2, label='Robot 1 Path')
        self.robot2_traj_plot, = self.ax.plot([], [], 'b-', alpha=0.5, linewidth=2, label='Robot 2 Path')

        # Point cloud plots (with different colors for each robot)
        self.pcl1_plot, = self.ax.plot([], [], 'r.', markersize=4, alpha=0.7, label='Robot 1 PCL')
        self.pcl2_plot, = self.ax.plot([], [], 'b.', markersize=4, alpha=0.7, label='Robot 2 PCL')

        # Add legend
        self.ax.legend(loc='upper right', bbox_to_anchor=(1.18, 1), fontsize=9)
        
        # Add info text
        self.info_text = self.ax.text(0.02, 0.98, '', transform=self.ax.transAxes,
                                     verticalalignment='top', fontsize=10,
                                     bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        # Add controls text
        controls_text = "Controls: SPACE=Pause/Resume, ↑/↓=Speed, R=Reset"
        self.ax.text(0.02, 0.02, controls_text, transform=self.ax.transAxes,
                    fontsize=9, bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7))

        # Connect keyboard events
        self.fig.canvas.mpl_connect('key_press_event', self.on_key_press)
        
    def draw_workstations(self):
        """Draw workstation positions on the plot."""
        for ws_name, ws_data in self.workstations.items():
            # Draw workstation as a square
            ws_rect = Rectangle(
                (ws_data['x'] - 0.3, ws_data['y'] - 0.3), 0.6, 0.6,
                facecolor=ws_data['color'], alpha=0.7, edgecolor='black', linewidth=2
            )
            self.ax.add_patch(ws_rect)
            
            # Add workstation label
            self.ax.text(ws_data['x'], ws_data['y'], ws_name, 
                        ha='center', va='center', fontweight='bold', fontsize=8, color='white')
    
    def get_frame_data(self, frame_idx):
        """Extract data for a specific frame."""
        if frame_idx >= len(self.data):
            return None
            
        row = self.data.iloc[frame_idx]
        
        # Extract robot positions (global coordinates)
        robot1_pos = (row['robot_1_global_x'], row['robot_1_global_y'])
        robot2_pos = (row['robot_2_global_x'], row['robot_2_global_y'])
        
        # Extract point cloud data (global radar coordinates)
        pcl1_pos = (row['robot_1_global_x_radar'], row['robot_1_global_y_radar'])
        pcl2_pos = (row['robot_2_global_x_radar'], row['robot_2_global_y_radar'])
        
        # Extract timestamps
        vicon_time = row['vicon_timestamp']
        
        return {
            'robot1_pos': robot1_pos,
            'robot2_pos': robot2_pos,
            'pcl1_pos': pcl1_pos,
            'pcl2_pos': pcl2_pos,
            'timestamp': vicon_time,
            'frame_idx': frame_idx
        }
    
    def update_frame(self, frame_num):
        """Update animation frame."""
        frame_data = self.get_frame_data(self.current_frame)

        if frame_data is None:
            # Reset to beginning
            self.current_frame = 0
            self.point_cloud_history = []
            return (self.robot1_plot, self.robot2_plot, self.robot1_traj_plot, self.robot2_traj_plot,
                    self.pcl1_plot, self.pcl2_plot, self.info_text)

        # Update robot positions and trajectories
        self.robot1_plot.set_data([frame_data['robot1_pos'][0]], [frame_data['robot1_pos'][1]])
        self.robot2_plot.set_data([frame_data['robot2_pos'][0]], [frame_data['robot2_pos'][1]])

        # Update robot trajectories
        self.robot1_trajectory.append(frame_data['robot1_pos'])
        self.robot2_trajectory.append(frame_data['robot2_pos'])

        # Limit trajectory length
        if len(self.robot1_trajectory) > self.max_trajectory:
            self.robot1_trajectory.pop(0)
        if len(self.robot2_trajectory) > self.max_trajectory:
            self.robot2_trajectory.pop(0)

        # Update trajectory plots
        if len(self.robot1_trajectory) > 1:
            traj1_x, traj1_y = zip(*self.robot1_trajectory)
            self.robot1_traj_plot.set_data(traj1_x, traj1_y)

        if len(self.robot2_trajectory) > 1:
            traj2_x, traj2_y = zip(*self.robot2_trajectory)
            self.robot2_traj_plot.set_data(traj2_x, traj2_y)

        # Add current point cloud to history
        self.point_cloud_history.append({
            'pcl1': frame_data['pcl1_pos'],
            'pcl2': frame_data['pcl2_pos'],
            'alpha': 1.0
        })

        # Limit history size and fade older points
        if len(self.point_cloud_history) > self.max_history:
            self.point_cloud_history.pop(0)

        # Update alpha values for fading effect
        for i, pcl_data in enumerate(self.point_cloud_history):
            pcl_data['alpha'] = (i + 1) / len(self.point_cloud_history)

        # Collect all point cloud data for plotting
        pcl1_x, pcl1_y = [], []
        pcl2_x, pcl2_y = [], []

        for pcl_data in self.point_cloud_history:
            # Add Robot 1 point cloud
            if not np.isnan(pcl_data['pcl1'][0]) and not np.isnan(pcl_data['pcl1'][1]):
                # Filter points within warehouse bounds
                if (self.warehouse_bounds['x_min'] <= pcl_data['pcl1'][0] <= self.warehouse_bounds['x_max'] and
                    self.warehouse_bounds['y_min'] <= pcl_data['pcl1'][1] <= self.warehouse_bounds['y_max']):
                    pcl1_x.append(pcl_data['pcl1'][0])
                    pcl1_y.append(pcl_data['pcl1'][1])

            # Add Robot 2 point cloud
            if not np.isnan(pcl_data['pcl2'][0]) and not np.isnan(pcl_data['pcl2'][1]):
                # Filter points within warehouse bounds
                if (self.warehouse_bounds['x_min'] <= pcl_data['pcl2'][0] <= self.warehouse_bounds['x_max'] and
                    self.warehouse_bounds['y_min'] <= pcl_data['pcl2'][1] <= self.warehouse_bounds['y_max']):
                    pcl2_x.append(pcl_data['pcl2'][0])
                    pcl2_y.append(pcl_data['pcl2'][1])

        self.pcl1_plot.set_data(pcl1_x, pcl1_y)
        self.pcl2_plot.set_data(pcl2_x, pcl2_y)

        # Calculate distances to workstations for proximity analysis
        ws_distances = self.calculate_workstation_distances(frame_data)

        # Update info text with enhanced information
        timestamp_readable = time.strftime('%H:%M:%S', time.localtime(frame_data['timestamp']))
        progress = (frame_data['frame_idx'] / len(self.data)) * 100

        info_str = f"Frame: {frame_data['frame_idx']}/{len(self.data)} ({progress:.1f}%)\n"
        info_str += f"Time: {timestamp_readable}\n"
        info_str += f"Robot 1 (ep03): ({frame_data['robot1_pos'][0]:.2f}, {frame_data['robot1_pos'][1]:.2f})\n"
        info_str += f"Robot 2 (ep05): ({frame_data['robot2_pos'][0]:.2f}, {frame_data['robot2_pos'][1]:.2f})\n"
        info_str += f"PCL Points: R1({len(pcl1_x)}), R2({len(pcl2_x)})\n"

        # Add nearest workstation info
        if ws_distances:
            nearest_ws = min(ws_distances.items(), key=lambda x: x[1])
            info_str += f"Nearest WS: {nearest_ws[0]} ({nearest_ws[1]:.2f}m)"

        self.info_text.set_text(info_str)

        # Advance frame
        self.current_frame += 1

        return (self.robot1_plot, self.robot2_plot, self.robot1_traj_plot, self.robot2_traj_plot,
                self.pcl1_plot, self.pcl2_plot, self.info_text)

    def calculate_workstation_distances(self, frame_data):
        """Calculate distances from robots to all workstations."""
        distances = {}
        robot1_pos = frame_data['robot1_pos']

        for ws_name, ws_data in self.workstations.items():
            dist = np.sqrt((robot1_pos[0] - ws_data['x'])**2 + (robot1_pos[1] - ws_data['y'])**2)
            distances[ws_name] = dist

        return distances

    def on_key_press(self, event):
        """Handle keyboard events."""
        if event.key == ' ':  # Space bar - pause/resume
            self.paused = not self.paused
            if self.paused:
                self.anim.pause()
                print("⏸️  Paused - Press SPACE to resume")
            else:
                self.anim.resume()
                print("▶️  Resumed")

        elif event.key == 'up':  # Speed up
            self.playback_speed = max(10, self.playback_speed - 10)
            print(f"⚡ Speed: {self.playback_speed}ms")

        elif event.key == 'down':  # Slow down
            self.playback_speed = min(200, self.playback_speed + 10)
            print(f"🐌 Speed: {self.playback_speed}ms")

        elif event.key == 'r':  # Reset
            self.current_frame = 0
            self.point_cloud_history = []
            self.robot1_trajectory = []
            self.robot2_trajectory = []
            print("🔄 Reset to beginning")

    def print_statistics(self):
        """Print dataset statistics."""
        print("\n📈 DATASET STATISTICS")
        print("=" * 30)
        print(f"Total frames: {len(self.data)}")
        print(f"Duration: {(self.data.iloc[-1]['vicon_timestamp'] - self.data.iloc[0]['vicon_timestamp']):.1f} seconds")
        print(f"Warehouse bounds: {self.warehouse_bounds['x_max'] - self.warehouse_bounds['x_min']:.1f}m x {self.warehouse_bounds['y_max'] - self.warehouse_bounds['y_min']:.1f}m")
        print(f"Workstations: {len(self.workstations)}")
        print("=" * 30)
    
    def start_animation(self):
        """Start the real-time animation."""
        print("🎬 Starting real-time visualization...")
        print("Controls: SPACE=Pause/Resume, ↑/↓=Speed, R=Reset, Ctrl+C=Exit")

        # Print statistics
        self.print_statistics()

        # Create animation
        self.anim = animation.FuncAnimation(
            self.fig, self.update_frame,
            interval=self.playback_speed,
            blit=True,
            repeat=True
        )

        # Show plot
        plt.tight_layout()
        plt.show()

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Real-time Warehouse Robotics Visualizer')
    parser.add_argument('--csv', type=str, 
                       default='data/03_transformed/Layout_01/transformed_dataset_20250219_121000.csv',
                       help='Path to transformed CSV dataset')
    parser.add_argument('--speed', type=int, default=50,
                       help='Animation speed in milliseconds (default: 50ms)')
    
    args = parser.parse_args()
    
    try:
        # Create visualizer
        visualizer = WarehouseRealtimeVisualizer(args.csv)
        visualizer.playback_speed = args.speed
        
        # Start visualization
        visualizer.start_animation()
        
    except KeyboardInterrupt:
        print("\n🛑 Visualization stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
