#!/usr/bin/env python3
"""
Example Latency Demo

Shows what the latency output looks like when processing ROS bag data.
"""

import os
import sys

def print_example_output():
    """Show example of the clean latency output"""
    print("="*50)
    print("EXAMPLE: CLEAN LATENCY PRINTING OUTPUT")
    print("="*50)
    print()

    print("FRAME 1 | 1739983771.527s")
    print("Sync: 1.8ms")
    print("  ep03: 0.3ms")
    print("Transform: 2.9ms")
    print("Filter: 5.2ms")
    print("Graph: 15.7ms")
    print("  points=47, nodes=23, edges=156")
    print("  combine=1.2ms, convert=14.5ms")
    print("Inference: 4.8ms")
    print("  gpu_transfer=0.8ms, model=3.2ms, cpu_transfer=0.4ms")
    print("TOTAL: 30.4ms")
    print("Nodes: 23 | Avg Occupancy: 0.742")
    print()

    print("FRAME 2 | 1739983771.660s")
    print("Sync: 1.5ms")
    print("  ep03: 0.2ms")
    print("Transform: 2.1ms")
    print("Filter: 4.8ms")
    print("Graph: 14.2ms")
    print("  points=52, nodes=28, edges=184")
    print("  combine=1.1ms, convert=13.1ms")
    print("Inference: 4.3ms")
    print("  gpu_transfer=0.7ms, model=2.9ms, cpu_transfer=0.4ms")
    print("TOTAL: 26.9ms")
    print("Nodes: 28 | Avg Occupancy: 0.681")
    print()

    print("FRAME 3 | 1739983771.793s")
    print("Sync: 1.2ms")
    print("  ep03: 0.2ms")
    print("Transform: 1.8ms")
    print("Filter: 4.1ms")
    print("Graph: 13.8ms")
    print("  points=41, nodes=19, edges=132")
    print("  combine=0.9ms, convert=12.9ms")
    print("Inference: 3.9ms")
    print("  gpu_transfer=0.6ms, model=2.7ms, cpu_transfer=0.3ms")
    print("TOTAL: 24.8ms")
    print("Nodes: 19 | Avg Occupancy: 0.598")
    print()

def show_usage_examples():
    """Show usage examples for different verbosity levels"""
    print("\n" + "="*70)
    print("USAGE EXAMPLES")
    print("="*70)
    print()
    
    print("1. VERBOSE MODE (default) - Shows detailed timing for each step:")
    print("   python live_rosbag_processor.py --rosbag /path/to/bag --model /path/to/model")
    print()
    
    print("2. QUIET MODE - Shows only summary timing:")
    print("   python live_rosbag_processor.py --rosbag /path/to/bag --model /path/to/model --quiet")
    print()
    print("   Output in quiet mode:")
    print("   🔄 FRAME 1 | Timestamp: 1739983771.527s")
    print("   ─" * 60)
    print("   📡 Stage 1: Data Synchronization... ✓ 1.8ms")
    print("   🌐 Stage 2: Coordinate Transform... ✓ 2.9ms")
    print("   🔍 Stage 3: Point Cloud Filter... ✓ 5.2ms")
    print("   🕸️  Stage 4: Graph Conversion... ✓ 15.7ms")
    print("   🧠 Stage 5: Model Inference... ✓ 4.8ms")
    print("   ⚡ TOTAL: 30.4ms | Target: 25ms | ❌ TOO SLOW | 32.9 FPS | Margin: -5.4ms")
    print("   📊 Nodes: 23 | Avg Occupancy: 0.742")
    print("   ─" * 60)
    print()
    
    print("3. PERFORMANCE TEST MODE - No visualization, focus on speed:")
    print("   python live_rosbag_processor.py --rosbag /path/to/bag --model /path/to/model --no-viz --quiet")
    print()
    
    print("4. SLOW ANALYSIS MODE - Detailed timing with slow playback:")
    print("   python live_rosbag_processor.py --rosbag /path/to/bag --model /path/to/model --playback-rate 0.5")

def show_timing_breakdown():
    """Show what each timing measurement represents"""
    print("\n" + "="*70)
    print("TIMING BREAKDOWN EXPLANATION")
    print("="*70)
    print()
    
    print("📡 STAGE 1: DATA SYNCHRONIZATION")
    print("   - search: Time to find closest radar/vicon messages")
    print("   - radar_diff: Time difference between target and found radar message")
    print("   - vicon_diff: Time difference between target and found vicon message")
    print("   - sync_total: Total synchronization time (if > 1ms)")
    print()
    
    print("🌐 STAGE 2: COORDINATE TRANSFORMATION")
    print("   - Transform local robot coordinates to global frame using Vicon poses")
    print("   - Apply translation and rotation matrices")
    print()
    
    print("🔍 STAGE 3: POINT CLOUD FILTERING")
    print("   - Filter points within arena bounds (20.7m x 9.92m)")
    print("   - Remove statistical outliers and noise")
    print()
    
    print("🕸️  STAGE 4: GRAPH CONVERSION")
    print("   - points: Total radar points from all robots")
    print("   - nodes: Number of graph nodes after voxelization")
    print("   - edges: Number of spatial connections between nodes")
    print("   - combine: Time to combine multi-robot data")
    print("   - convert: Time for actual graph generation")
    print()
    
    print("🧠 STAGE 5: MODEL INFERENCE")
    print("   - transfer_to_gpu: Move data to GPU memory")
    print("   - batch: Create batch tensor for PyTorch Geometric")
    print("   - model_forward: GNN forward pass computation")
    print("   - sigmoid: Apply sigmoid activation for probabilities")
    print("   - transfer_to_cpu: Move results back to CPU")
    print()
    
    print("⚡ PERFORMANCE SUMMARY")
    print("   - TOTAL: Complete pipeline latency")
    print("   - Target: Maximum allowed latency (25ms for 40 FPS)")
    print("   - Status: ✅ REAL-TIME or ❌ TOO SLOW")
    print("   - FPS: Achieved frames per second")
    print("   - Margin: Difference from target (+good, -bad)")
    print()
    
    print("📊 PREDICTION RESULTS")
    print("   - Nodes: Number of spatial locations processed")
    print("   - Avg Occupancy: Average predicted occupancy probability")

def main():
    print("LIVE ROS BAG PROCESSOR - LATENCY PRINTING DEMO")
    
    # Show example output
    print_example_output()
    
    # Show usage examples
    show_usage_examples()
    
    # Show timing breakdown
    show_timing_breakdown()
    
    print("\n" + "="*70)
    print("READY TO RUN LIVE PROCESSING!")
    print("="*70)
    print()
    print("Use the test script first:")
    print("  python test_rosbag_processor.py --rosbag /path/to/bag --model /path/to/model")
    print()
    print("Then run live processing:")
    print("  python live_rosbag_processor.py --rosbag /path/to/bag --model /path/to/model")
    print()
    print("For your specific ROS bag format:")
    print("  python live_rosbag_processor.py \\")
    print("    --rosbag /path/to/20250219_174928 \\")
    print("    --model models/checkpoints_standard_gatv2_t3/model_temporal_3_best.pt \\")
    print("    --arena-bounds 0 20.7 0 9.92")

if __name__ == '__main__':
    main()
