#!/usr/bin/env python3
"""
Demo Script for Live ROS Bag Processing

This script demonstrates how to use the live ROS bag processor with your specific
ROS bag format and provides examples for different scenarios.

Based on your ROS bag metadata:
- Duration: 378 seconds (~6.3 minutes)
- Topics: /ep03/vicon/pose, /ep03/camera_raw, /ep03/ti_mmwave/radar_scan_pcl, /ep03/imu, /ep03/odom
- Robot: ep03 (single robot scenario)
"""

import os
import sys
import time
import argparse
import subprocess
from pathlib import Path

def print_rosbag_info(rosbag_path):
    """Print information about the ROS bag"""
    print(f"ROS Bag Analysis: {rosbag_path}")
    print("-" * 50)
    
    # Your specific ROS bag metadata
    metadata = {
        'duration_seconds': 378.04,
        'message_count': 66704,
        'topics': {
            '/ep03/vicon/pose': {'type': 'geometry_msgs/msg/TransformStamped', 'count': 14716},
            '/ep03/camera_raw': {'type': 'sensor_msgs/msg/Image', 'count': 11328},
            '/ep03/ti_mmwave/radar_scan_pcl': {'type': 'sensor_msgs/msg/PointCloud2', 'count': 2854},
            '/ep03/imu': {'type': 'sensor_msgs/msg/Imu', 'count': 18903},
            '/ep03/odom': {'type': 'nav_msgs/msg/Odometry', 'count': 18903}
        }
    }
    
    print(f"Duration: {metadata['duration_seconds']:.1f} seconds")
    print(f"Total messages: {metadata['message_count']:,}")
    print(f"Robot ID: ep03")
    print("\nTopics:")
    for topic, info in metadata['topics'].items():
        print(f"  {topic}")
        print(f"    Type: {info['type']}")
        print(f"    Messages: {info['count']:,}")
        if 'radar_scan_pcl' in topic:
            rate = info['count'] / metadata['duration_seconds']
            print(f"    Rate: {rate:.1f} Hz")
    
    print(f"\nRadar data rate: {2854/378.04:.1f} Hz (~7.5 Hz)")
    print(f"Vicon data rate: {14716/378.04:.1f} Hz (~39 Hz)")

def demo_basic_processing(rosbag_path, model_path):
    """Demo basic live processing"""
    print("\n" + "="*60)
    print("DEMO 1: BASIC LIVE PROCESSING")
    print("="*60)
    
    cmd = [
        'python', 'live_rosbag_processor.py',
        '--rosbag', rosbag_path,
        '--model', model_path,
        '--voxel-size', '0.1',
        '--arena-bounds', '0', '20.7', '0', '9.92'
    ]
    
    print("Command:")
    print(" ".join(cmd))
    print("\nThis will:")
    print("- Process radar data from /ep03/ti_mmwave/radar_scan_pcl")
    print("- Use Vicon poses from /ep03/vicon/pose for coordinate transformation")
    print("- Apply 0.1m voxelization for graph conversion")
    print("- Target 25ms latency (40 FPS)")
    print("- Show real-time visualization")
    
    response = input("\nRun this demo? (y/n): ")
    if response.lower() == 'y':
        subprocess.run(cmd)

def demo_performance_test(rosbag_path, model_path):
    """Demo performance testing without visualization"""
    print("\n" + "="*60)
    print("DEMO 2: PERFORMANCE TEST (NO VISUALIZATION)")
    print("="*60)
    
    cmd = [
        'python', 'live_rosbag_processor.py',
        '--rosbag', rosbag_path,
        '--model', model_path,
        '--no-viz',
        '--voxel-size', '0.1'
    ]
    
    print("Command:")
    print(" ".join(cmd))
    print("\nThis will:")
    print("- Run without visualization for maximum performance")
    print("- Focus on processing speed optimization")
    print("- Print detailed timing metrics")
    
    response = input("\nRun this demo? (y/n): ")
    if response.lower() == 'y':
        subprocess.run(cmd)

def demo_slow_playback(rosbag_path, model_path):
    """Demo with slow playback for detailed analysis"""
    print("\n" + "="*60)
    print("DEMO 3: SLOW PLAYBACK FOR ANALYSIS")
    print("="*60)
    
    cmd = [
        'python', 'live_rosbag_processor.py',
        '--rosbag', rosbag_path,
        '--model', model_path,
        '--playback-rate', '0.5',
        '--voxel-size', '0.05'  # Higher resolution
    ]
    
    print("Command:")
    print(" ".join(cmd))
    print("\nThis will:")
    print("- Play bag at 0.5x speed for detailed analysis")
    print("- Use 0.05m voxels for higher spatial resolution")
    print("- Allow 50ms latency for more detailed processing")
    
    response = input("\nRun this demo? (y/n): ")
    if response.lower() == 'y':
        subprocess.run(cmd)

def demo_different_models(rosbag_path):
    """Demo with different model architectures"""
    print("\n" + "="*60)
    print("DEMO 4: DIFFERENT MODEL ARCHITECTURES")
    print("="*60)
    
    models = [
        ('GATv2 (T=3)', 'models/checkpoints_standard_gatv2_t3/model_temporal_3_best.pt'),
        ('GATv2 (T=5)', 'models/checkpoints_standard_gatv2_t5/model_temporal_5_best.pt'),
        ('ECC (T=3)', 'models/checkpoints_ecc_t3/model_temporal_3_best.pt'),
        ('ECC (T=5)', 'models/checkpoints_ecc_t5/model_temporal_5_best.pt')
    ]
    
    print("Available models:")
    for i, (name, path) in enumerate(models):
        exists = "✓" if os.path.exists(path) else "❌"
        print(f"  {i+1}. {name} {exists}")
    
    choice = input("\nSelect model (1-4): ")
    try:
        model_idx = int(choice) - 1
        if 0 <= model_idx < len(models):
            name, model_path = models[model_idx]
            
            if not os.path.exists(model_path):
                print(f"❌ Model not found: {model_path}")
                return
            
            print(f"\nTesting with {name}")
            
            cmd = [
                'python', 'live_rosbag_processor.py',
                '--rosbag', rosbag_path,
                '--model', model_path,
                '--voxel-size', '0.1'
            ]
            
            subprocess.run(cmd)
        else:
            print("Invalid choice")
    except ValueError:
        print("Invalid choice")

def run_test_suite(rosbag_path, model_path):
    """Run the complete test suite"""
    print("\n" + "="*60)
    print("RUNNING TEST SUITE")
    print("="*60)
    
    cmd = [
        'python', 'test_rosbag_processor.py',
        '--rosbag', rosbag_path,
        '--model', model_path
    ]
    
    print("Command:")
    print(" ".join(cmd))
    
    result = subprocess.run(cmd)
    return result.returncode == 0

def main():
    parser = argparse.ArgumentParser(
        description="Demo Live ROS Bag Processing",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --rosbag /path/to/20250219_174928 --model models/checkpoints_standard_gatv2_t3/model_temporal_3_best.pt
  %(prog)s --rosbag /path/to/rosbag --model /path/to/model --test-only
        """
    )
    
    parser.add_argument(
        '--rosbag', type=str, required=True,
        help='Path to ROS bag directory'
    )
    
    parser.add_argument(
        '--model', type=str, 
        default='models/checkpoints_standard_gatv2_t3/model_temporal_3_best.pt',
        help='Path to trained model checkpoint'
    )
    
    parser.add_argument(
        '--test-only', action='store_true',
        help='Run test suite only'
    )
    
    args = parser.parse_args()
    
    # Validate inputs
    if not os.path.exists(args.rosbag):
        print(f"❌ ROS bag path does not exist: {args.rosbag}")
        return 1
    
    if not os.path.exists(args.model):
        print(f"❌ Model file does not exist: {args.model}")
        return 1
    
    print("="*60)
    print("LIVE ROS BAG PROCESSING - DEMO")
    print("="*60)
    
    # Print ROS bag information
    print_rosbag_info(args.rosbag)
    
    # Run test suite first
    print("\nRunning test suite...")
    if not run_test_suite(args.rosbag, args.model):
        print("❌ Test suite failed. Please fix issues before running demos.")
        return 1
    
    if args.test_only:
        print("✅ Test suite completed successfully!")
        return 0
    
    # Interactive demo menu
    while True:
        print("\n" + "="*60)
        print("DEMO MENU")
        print("="*60)
        print("1. Basic Live Processing (with visualization)")
        print("2. Performance Test (no visualization)")
        print("3. Slow Playback Analysis")
        print("4. Different Model Architectures")
        print("5. Exit")
        
        choice = input("\nSelect demo (1-5): ")
        
        if choice == '1':
            demo_basic_processing(args.rosbag, args.model)
        elif choice == '2':
            demo_performance_test(args.rosbag, args.model)
        elif choice == '3':
            demo_slow_playback(args.rosbag, args.model)
        elif choice == '4':
            demo_different_models(args.rosbag)
        elif choice == '5':
            break
        else:
            print("Invalid choice. Please select 1-5.")
    
    print("\nDemo completed!")
    return 0

if __name__ == '__main__':
    exit(main())
