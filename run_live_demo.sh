#!/bin/bash

# Live ROS Bag Processing Demo Script
# This script provides a complete workflow for running live ROS bag processing

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default paths (update these for your system)
DEFAULT_ROSBAG_PATH="/path/to/your/rosbag"
DEFAULT_MODEL_PATH="models/checkpoints_standard_gatv2_t3/model_temporal_3_best.pt"

echo -e "${BLUE}================================================================${NC}"
echo -e "${BLUE}           LIVE ROS BAG PROCESSOR - QUICK START${NC}"
echo -e "${BLUE}================================================================${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if ROS2 is sourced
check_ros2() {
    if ! command -v ros2 &> /dev/null; then
        print_error "ROS2 not found. Please source ROS2 setup:"
        echo "  source /opt/ros/humble/setup.bash"
        exit 1
    fi
    print_status "ROS2 found: $(ros2 --version)"
}

# Check if Python dependencies are available
check_python_deps() {
    python3 -c "import torch, torch_geometric, numpy, pandas, matplotlib, rclpy" 2>/dev/null
    if [ $? -eq 0 ]; then
        print_status "Python dependencies available"
    else
        print_error "Missing Python dependencies. Run setup:"
        echo "  python3 setup_live_processor.py"
        exit 1
    fi
}

# Get ROS bag path from user
get_rosbag_path() {
    if [ -z "$1" ]; then
        echo -e "\n${YELLOW}Enter ROS bag path:${NC}"
        read -p "ROS bag path: " ROSBAG_PATH
        
        if [ -z "$ROSBAG_PATH" ]; then
            ROSBAG_PATH=$DEFAULT_ROSBAG_PATH
        fi
    else
        ROSBAG_PATH=$1
    fi
    
    if [ ! -d "$ROSBAG_PATH" ]; then
        print_error "ROS bag directory not found: $ROSBAG_PATH"
        exit 1
    fi
    
    print_status "Using ROS bag: $ROSBAG_PATH"
}

# Get model path from user
get_model_path() {
    if [ -z "$1" ]; then
        echo -e "\n${YELLOW}Available models:${NC}"
        find models -name "*.pt" -type f 2>/dev/null | head -5
        
        echo -e "\n${YELLOW}Enter model path (or press Enter for default):${NC}"
        read -p "Model path: " MODEL_PATH
        
        if [ -z "$MODEL_PATH" ]; then
            MODEL_PATH=$DEFAULT_MODEL_PATH
        fi
    else
        MODEL_PATH=$1
    fi
    
    if [ ! -f "$MODEL_PATH" ]; then
        print_error "Model file not found: $MODEL_PATH"
        exit 1
    fi
    
    print_status "Using model: $MODEL_PATH"
}

# Run setup if needed
run_setup() {
    if [ ! -f "live_rosbag_processor.py" ]; then
        print_error "Live processor script not found. Please ensure you're in the correct directory."
        exit 1
    fi
    
    print_info "Running setup check..."
    python3 setup_live_processor.py
}

# Run test suite
run_tests() {
    print_info "Running test suite..."
    python3 test_rosbag_processor.py --rosbag "$ROSBAG_PATH" --model "$MODEL_PATH"
    
    if [ $? -ne 0 ]; then
        print_error "Tests failed. Please fix issues before proceeding."
        exit 1
    fi
    
    print_status "All tests passed!"
}

# Show demo menu
show_demo_menu() {
    echo -e "\n${BLUE}================================================================${NC}"
    echo -e "${BLUE}                        DEMO MENU${NC}"
    echo -e "${BLUE}================================================================${NC}"
    echo "1. Quick Demo (basic processing with visualization)"
    echo "2. Performance Test (no visualization, focus on speed)"
    echo "3. Interactive Demo (full demo suite)"
    echo "4. Custom Parameters"
    echo "5. Exit"
    echo ""
}

# Run quick demo
run_quick_demo() {
    print_info "Starting quick demo..."
    python3 live_rosbag_processor.py \
        --rosbag "$ROSBAG_PATH" \
        --model "$MODEL_PATH" \
        --voxel-size 0.1 \
        --max-latency 25.0 \
        --arena-bounds 0 20.7 0 9.92
}

# Run performance test
run_performance_test() {
    print_info "Starting performance test..."
    python3 live_rosbag_processor.py \
        --rosbag "$ROSBAG_PATH" \
        --model "$MODEL_PATH" \
        --no-viz \
        --voxel-size 0.1 \
        --max-latency 25.0
}

# Run interactive demo
run_interactive_demo() {
    print_info "Starting interactive demo..."
    python3 demo_live_processing.py \
        --rosbag "$ROSBAG_PATH" \
        --model "$MODEL_PATH"
}

# Run with custom parameters
run_custom_demo() {
    echo -e "\n${YELLOW}Custom Parameters:${NC}"
    
    read -p "Voxel size (default 0.1): " VOXEL_SIZE
    VOXEL_SIZE=${VOXEL_SIZE:-0.1}
    
    read -p "Max latency ms (default 25): " MAX_LATENCY
    MAX_LATENCY=${MAX_LATENCY:-25}
    
    read -p "Playback rate (default 1.0): " PLAYBACK_RATE
    PLAYBACK_RATE=${PLAYBACK_RATE:-1.0}
    
    read -p "Disable visualization? (y/n): " NO_VIZ
    
    CMD="python3 live_rosbag_processor.py --rosbag \"$ROSBAG_PATH\" --model \"$MODEL_PATH\" --voxel-size $VOXEL_SIZE --max-latency $MAX_LATENCY --playback-rate $PLAYBACK_RATE"
    
    if [ "$NO_VIZ" = "y" ]; then
        CMD="$CMD --no-viz"
    fi
    
    print_info "Running: $CMD"
    eval $CMD
}

# Main execution
main() {
    # Parse command line arguments
    ROSBAG_PATH=""
    MODEL_PATH=""
    SKIP_SETUP=false
    SKIP_TESTS=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --rosbag)
                ROSBAG_PATH="$2"
                shift 2
                ;;
            --model)
                MODEL_PATH="$2"
                shift 2
                ;;
            --skip-setup)
                SKIP_SETUP=true
                shift
                ;;
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            -h|--help)
                echo "Usage: $0 [--rosbag PATH] [--model PATH] [--skip-setup] [--skip-tests]"
                echo ""
                echo "Options:"
                echo "  --rosbag PATH    Path to ROS bag directory"
                echo "  --model PATH     Path to model checkpoint"
                echo "  --skip-setup     Skip setup check"
                echo "  --skip-tests     Skip test suite"
                echo "  -h, --help       Show this help"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Step 1: Check environment
    print_info "Checking environment..."
    check_ros2
    check_python_deps
    
    # Step 2: Setup (optional)
    if [ "$SKIP_SETUP" = false ]; then
        run_setup
    fi
    
    # Step 3: Get paths
    get_rosbag_path "$ROSBAG_PATH"
    get_model_path "$MODEL_PATH"
    
    # Step 4: Run tests (optional)
    if [ "$SKIP_TESTS" = false ]; then
        run_tests
    fi
    
    # Step 5: Demo menu
    while true; do
        show_demo_menu
        read -p "Select option (1-5): " choice
        
        case $choice in
            1)
                run_quick_demo
                ;;
            2)
                run_performance_test
                ;;
            3)
                run_interactive_demo
                ;;
            4)
                run_custom_demo
                ;;
            5)
                print_info "Goodbye!"
                exit 0
                ;;
            *)
                print_warning "Invalid choice. Please select 1-5."
                ;;
        esac
        
        echo ""
        read -p "Press Enter to continue..."
    done
}

# Run main function
main "$@"
