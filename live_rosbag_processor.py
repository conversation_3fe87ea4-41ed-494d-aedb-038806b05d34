#!/usr/bin/env python3
"""
Live ROS Bag Processor

This script reads ROS bag data and does preprocessing and inference.
It processes radar data from robots and predicts occupancy using a trained model.

Author: Student
"""

import os
import sys
import time
import sqlite3
import subprocess
import numpy as np
import pandas as pd
import torch
import yaml
import matplotlib.pyplot as plt
from pathlib import Path

# ROS2 imports
import rclpy
from rclpy.node import Node
from sensor_msgs.msg import PointCloud2
from geometry_msgs.msg import TransformStamped
import sensor_msgs_py.point_cloud2 as pc2

# Add project paths so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'gnn_training'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'preprocess scripts'))

# Try to import our modules
try:
    from model import create_model
    from point_cloud_to_graph_converter import create_collaborative_gnn_frame
except ImportError as e:
    print(f"Warning: Could not import some modules: {e}")
    print("Make sure you're running from the correct directory")

# Simple configuration class
class ProcessingConfig:
    def __init__(self, rosbag_path, model_path, config_path,
                 voxel_size=0.1, temporal_window=3,
                 visualization=True, verbose_timing=True,
                 arena_bounds=(0, 20.7, 0, 9.92)):
        self.rosbag_path = rosbag_path
        self.model_path = model_path
        self.config_path = config_path
        self.voxel_size = voxel_size
        self.temporal_window = temporal_window
        self.visualization = visualization
        self.verbose_timing = verbose_timing
        self.arena_bounds = arena_bounds  # x_min, x_max, y_min, y_max

class LiveROSBagProcessor(Node):
    """
    Simple ROS Bag Processor

    Reads ROS bag data and does inference with a trained model.
    """

    def __init__(self, config):
        super().__init__('live_rosbag_processor')

        self.config = config
        self.processed_frames = []
        self.processing_times = []

        # Load model
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = self._load_model()

        # Data storage - simple dictionaries
        self.sensor_data_buffer = {}
        self.sensor_data_buffer['ep03'] = []
        self.sensor_data_buffer['ep05'] = []

        # Setup ROS subscriptions
        self._setup_subscriptions()

        # Setup visualization if needed
        if self.config.visualization:
            self._setup_visualization()
    
    def _load_model(self):
        """Load the trained model"""
        print(f"Loading model from {self.config.model_path}")

        # Read the config file
        with open(self.config.config_path, 'r') as f:
            model_config = yaml.safe_load(f)

        # Create the model
        model = create_model(model_config)

        # Load the saved weights
        checkpoint = torch.load(self.config.model_path, map_location=self.device)
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)

        # Put model on GPU/CPU and set to evaluation mode
        model.to(self.device)
        model.eval()

        print(f"Model loaded on {self.device}")
        return model
    
    def _setup_subscriptions(self):
        """Setup ROS subscriptions for sensor data"""
        # Robot 1 (ep03) subscriptions
        self.create_subscription(
            PointCloud2, '/ep03/ti_mmwave/radar_scan_pcl',
            lambda msg: self._radar_callback(msg, 'ep03'), 10)
        
        self.create_subscription(
            TransformStamped, '/ep03/vicon/pose',
            lambda msg: self._vicon_callback(msg, 'ep03'), 10)
        
        # Robot 2 (ep05) subscriptions if available
        self.create_subscription(
            PointCloud2, '/ep05/ti_mmwave/radar_scan_pcl',
            lambda msg: self._radar_callback(msg, 'ep05'), 10)
        
        self.create_subscription(
            TransformStamped, '/ep05/vicon/pose',
            lambda msg: self._vicon_callback(msg, 'ep05'), 10)
    
    def _setup_visualization(self):
        """Setup real-time visualization"""
        plt.ion()
        self.fig, self.axes = plt.subplots(2, 2, figsize=(15, 10))
        self.fig.suptitle('Live Collaborative Perception - Occupancy Prediction', fontsize=16)
        
        # Configure subplots
        self.axes[0, 0].set_title('Point Cloud Data')
        self.axes[0, 1].set_title('Graph Structure')
        self.axes[1, 0].set_title('Occupancy Predictions')
        self.axes[1, 1].set_title('Processing Performance')
    
    def _radar_callback(self, msg: PointCloud2, robot_id: str):
        """Process incoming radar data"""
        timestamp = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9
        
        # Convert point cloud to numpy array
        points = []
        for point in pc2.read_points(msg, field_names=("x", "y", "z"), skip_nans=True):
            points.append([point[0], point[1], point[2]])
        
        if points:
            radar_points = np.array(points)
            
            # Store in buffer
            self.sensor_data_buffer[robot_id].append({
                'timestamp': timestamp,
                'radar_points': radar_points,
                'type': 'radar'
            })
            
            # Trigger processing if we have recent data
            self._trigger_processing(timestamp)
    
    def _vicon_callback(self, msg: TransformStamped, robot_id: str):
        """Process incoming Vicon pose data"""
        timestamp = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9
        
        pose_data = {
            'x': msg.transform.translation.x,
            'y': msg.transform.translation.y,
            'z': msg.transform.translation.z,
            'qx': msg.transform.rotation.x,
            'qy': msg.transform.rotation.y,
            'qz': msg.transform.rotation.z,
            'qw': msg.transform.rotation.w
        }
        
        # Store in buffer
        self.sensor_data_buffer[robot_id].append({
            'timestamp': timestamp,
            'pose': pose_data,
            'type': 'vicon'
        })
    
    def _trigger_processing(self, current_timestamp: float):
        """Trigger processing when sufficient data is available"""
        # Check if we have recent data from both robots
        ep03_data = [d for d in self.sensor_data_buffer['ep03'] 
                     if abs(d['timestamp'] - current_timestamp) < 0.1]
        ep05_data = [d for d in self.sensor_data_buffer.get('ep05', []) 
                     if abs(d['timestamp'] - current_timestamp) < 0.1]
        
        if ep03_data and (ep05_data or len(self.sensor_data_buffer.get('ep05', [])) == 0):
            # Process the frame
            self._process_frame(current_timestamp)
    
    def _process_frame(self, timestamp: float):
        """Process a single frame through the complete pipeline"""
        start_time = time.time()
        frame_id = len(self.processed_frames) + 1

        print(f"\nFRAME {frame_id} | {timestamp:.3f}s")

        try:
            # Stage 1: Data Synchronization
            sync_start = time.time()
            synchronized_data = self._synchronize_data(timestamp)
            sync_time = (time.time() - sync_start) * 1000
            print(f"Sync: {sync_time:.1f}ms")

            if synchronized_data is None:
                print("No synchronized data available")
                return

            # Stage 2: Coordinate Transformation
            transform_start = time.time()
            transformed_data = self._transform_coordinates(synchronized_data)
            transform_time = (time.time() - transform_start) * 1000
            print(f"Transform: {transform_time:.1f}ms")

            # Stage 3: Point Cloud Filtering
            filter_start = time.time()
            filtered_data = self._filter_point_cloud(transformed_data)
            filter_time = (time.time() - filter_start) * 1000
            print(f"Filter: {filter_time:.1f}ms")

            # Stage 4: Graph Conversion
            graph_start = time.time()
            graph_data = self._convert_to_graph(filtered_data, timestamp)
            graph_time = (time.time() - graph_start) * 1000
            print(f"Graph: {graph_time:.1f}ms")

            # Stage 5: Model Inference
            inference_start = time.time()
            predictions = self._run_inference(graph_data)
            inference_time = (time.time() - inference_start) * 1000
            print(f"Inference: {inference_time:.1f}ms")

            total_time = (time.time() - start_time) * 1000
            
            # Store results
            result = {
                'timestamp': timestamp,
                'predictions': predictions,
                'graph_data': graph_data,
                'processing_times': {
                    'sync': sync_time,
                    'transform': transform_time,
                    'filter': filter_time,
                    'graph': graph_time,
                    'inference': inference_time,
                    'total': total_time
                }
            }
            
            self.processed_frames.append(result)
            self.processing_times.append(total_time)
            
            # Update visualization
            if self.config.visualization:
                self._update_visualization(result)
            
            # Print performance metrics
            self._print_performance_metrics(result)
            
        except Exception as e:
            self.get_logger().error(f"Error processing frame at {timestamp}: {e}")
    
    def _synchronize_data(self, target_timestamp):
        """Find the closest radar and vicon data for each robot"""
        # Simple synchronization - find closest data points
        synchronized = {}

        for robot_id in ['ep03', 'ep05']:
            if robot_id not in self.sensor_data_buffer:
                continue

            robot_data = self.sensor_data_buffer[robot_id]

            # Find radar and vicon data
            radar_search_start = time.time()
            radar_data = []
            vicon_data = []

            # Go through all data and separate radar from vicon
            for d in robot_data:
                if d['type'] == 'radar':
                    radar_data.append(d)
                elif d['type'] == 'vicon':
                    vicon_data.append(d)

            if len(radar_data) == 0:
                continue

            # Find the closest radar message to our target time
            closest_radar = None
            min_time_diff = float('inf')
            for radar in radar_data:
                time_diff = abs(radar['timestamp'] - target_timestamp)
                if time_diff < min_time_diff:
                    min_time_diff = time_diff
                    closest_radar = radar

            # Find the closest vicon message
            closest_vicon = None
            if len(vicon_data) > 0:
                min_time_diff = float('inf')
                for vicon in vicon_data:
                    time_diff = abs(vicon['timestamp'] - target_timestamp)
                    if time_diff < min_time_diff:
                        min_time_diff = time_diff
                        closest_vicon = vicon

            radar_search_time = (time.time() - radar_search_start) * 1000

            synchronized[robot_id] = {
                'radar': closest_radar,
                'vicon': closest_vicon
            }

            # Print timing if verbose mode
            if self.config.verbose_timing:
                print(f"  {robot_id}: {radar_search_time:.1f}ms")

        if len(synchronized) > 0:
            return synchronized
        else:
            return None

    def _transform_coordinates(self, synchronized_data):
        """Transform robot coordinates to global coordinates using Vicon"""
        transformed_data = {}

        for robot_id in synchronized_data:
            data = synchronized_data[robot_id]
            radar_data = data['radar']
            vicon_data = data['vicon']

            if vicon_data is None:
                # No Vicon data, just use the radar points as-is
                transformed_data[robot_id] = radar_data['radar_points']
                continue

            # Get the robot position from Vicon
            pose = vicon_data['pose']
            robot_x = pose['x']
            robot_y = pose['y']
            robot_z = pose['z']

            # Transform radar points to global coordinates
            # Simple translation (student level - not doing full rotation)
            points = radar_data['radar_points']
            transformed_points = points.copy()
            transformed_points[:, 0] += robot_x  # Add robot x position
            transformed_points[:, 1] += robot_y  # Add robot y position
            transformed_points[:, 2] += robot_z  # Add robot z position

            transformed_data[robot_id] = transformed_points

        return transformed_data

    def _filter_point_cloud(self, transformed_data):
        """Filter out points that are outside the arena or look like noise"""
        filtered_data = {}

        for robot_id in transformed_data:
            points = transformed_data[robot_id]

            if len(points) == 0:
                filtered_data[robot_id] = points
                continue

            # Filter points to only keep ones inside the arena
            x_min, x_max, y_min, y_max = self.config.arena_bounds

            # Check each point to see if it's in bounds
            good_points = []
            for i in range(len(points)):
                x = points[i, 0]
                y = points[i, 1]
                if x >= x_min and x <= x_max and y >= y_min and y <= y_max:
                    good_points.append(points[i])

            if len(good_points) > 0:
                filtered_points = np.array(good_points)

                # Remove outliers if we have enough points
                if len(filtered_points) > 10:
                    # Simple outlier removal - remove points too far from average
                    mean_x = np.mean(filtered_points[:, 0])
                    mean_y = np.mean(filtered_points[:, 1])
                    std_x = np.std(filtered_points[:, 0])
                    std_y = np.std(filtered_points[:, 1])

                    final_points = []
                    for point in filtered_points:
                        if (abs(point[0] - mean_x) < 3 * std_x and
                            abs(point[1] - mean_y) < 3 * std_y):
                            final_points.append(point)

                    if len(final_points) > 0:
                        filtered_data[robot_id] = np.array(final_points)
                    else:
                        filtered_data[robot_id] = filtered_points
                else:
                    filtered_data[robot_id] = filtered_points
            else:
                filtered_data[robot_id] = np.array([]).reshape(0, 3)

        return filtered_data

    def _convert_to_graph(self, filtered_data, timestamp):
        """Convert point cloud data to graph for the neural network"""
        # Combine data from all robots
        combine_start = time.time()
        all_points = []
        total_points = 0

        # Go through each robot's data
        for robot_id in filtered_data:
            points = filtered_data[robot_id]
            if len(points) == 0:
                continue

            # Make a dataframe with the points
            df_points = pd.DataFrame(points, columns=['x', 'y', 'z'])
            df_points['robot_id'] = robot_id
            df_points['timestamp'] = timestamp
            df_points['occupancy'] = 1  # radar detections mean something is there

            all_points.append(df_points)
            total_points += len(points)

        # Check if we have any points
        if len(all_points) == 0:
            if self.config.verbose_timing:
                print("  no points")
            return None

        # Combine all the dataframes
        combined_df = pd.concat(all_points, ignore_index=True)
        combine_time = (time.time() - combine_start) * 1000

        # Convert to graph format
        conversion_start = time.time()
        try:
            # Use the existing graph converter function
            graph_data = create_collaborative_gnn_frame(
                combined_df, timestamp, self.config.voxel_size
            )
            conversion_time = (time.time() - conversion_start) * 1000

            # Print some info about the graph
            if graph_data is not None and self.config.verbose_timing:
                num_nodes = len(graph_data.x) if hasattr(graph_data, 'x') else 0
                num_edges = len(graph_data.edge_index[0]) if hasattr(graph_data, 'edge_index') else 0
                print(f"  points={total_points}, nodes={num_nodes}, edges={num_edges}")
                print(f"  combine={combine_time:.1f}ms, convert={conversion_time:.1f}ms")

            return graph_data
        except Exception as e:
            conversion_time = (time.time() - conversion_start) * 1000
            if self.config.verbose_timing:
                print(f"  conversion error: {conversion_time:.1f}ms")
            print(f"Error converting to graph: {e}")
            return None

    def _run_inference(self, graph_data):
        """Run the neural network on the graph data"""
        if graph_data is None:
            if self.config.verbose_timing:
                print("  no graph data")
            return None

        try:
            # Don't compute gradients since we're just doing inference
            with torch.no_grad():
                # Move data to GPU/CPU
                data_transfer_start = time.time()
                x = graph_data.x.to(self.device)
                edge_index = graph_data.edge_index.to(self.device)
                data_transfer_time = (time.time() - data_transfer_start) * 1000

                # Create batch tensor (needed for PyTorch Geometric)
                batch = torch.zeros(x.size(0), dtype=torch.long, device=self.device)

                # Run the model
                model_start = time.time()
                predictions = self.model(x, edge_index, batch)
                # Apply sigmoid to get probabilities between 0 and 1
                probabilities = torch.sigmoid(predictions)
                model_time = (time.time() - model_start) * 1000

                # Move results back to CPU
                cpu_transfer_start = time.time()
                result_predictions = probabilities.cpu().numpy()
                result_positions = graph_data.pos.numpy()
                cpu_transfer_time = (time.time() - cpu_transfer_start) * 1000

                # Print timing breakdown
                if self.config.verbose_timing:
                    print(f"  gpu_transfer={data_transfer_time:.1f}ms, model={model_time:.1f}ms, cpu_transfer={cpu_transfer_time:.1f}ms")

                return {
                    'predictions': result_predictions,
                    'positions': result_positions,
                    'node_count': x.size(0)
                }

        except Exception as e:
            if self.config.verbose_timing:
                print("  inference error")
            print(f"Error during inference: {e}")
            return None

    def _update_visualization(self, result):
        """Update real-time visualization"""
        try:
            # Clear previous plots
            for ax in self.axes.flat:
                ax.clear()

            # Plot 1: Point Cloud Data
            ax1 = self.axes[0, 0]
            if result['graph_data'] is not None:
                pos = result['graph_data'].pos.numpy()
                ax1.scatter(pos[:, 0], pos[:, 1], c='blue', alpha=0.6, s=20)
                ax1.set_xlim(self.config.arena_bounds[0], self.config.arena_bounds[1])
                ax1.set_ylim(self.config.arena_bounds[2], self.config.arena_bounds[3])
                ax1.set_title(f'Point Cloud Data (Nodes: {len(pos)})')
                ax1.set_xlabel('X (m)')
                ax1.set_ylabel('Y (m)')
                ax1.grid(True, alpha=0.3)

            # Plot 2: Graph Structure (simplified)
            ax2 = self.axes[0, 1]
            if result['graph_data'] is not None:
                pos = result['graph_data'].pos.numpy()
                edge_index = result['graph_data'].edge_index.numpy()

                # Plot nodes
                ax2.scatter(pos[:, 0], pos[:, 1], c='lightblue', s=30)

                # Plot edges (sample for performance)
                if len(edge_index[0]) > 0:
                    sample_edges = min(100, len(edge_index[0]))  # Limit edges for visualization
                    for i in range(sample_edges):
                        src, dst = edge_index[0, i], edge_index[1, i]
                        ax2.plot([pos[src, 0], pos[dst, 0]],
                                [pos[src, 1], pos[dst, 1]], 'gray', alpha=0.3, linewidth=0.5)

                ax2.set_title(f'Graph Structure (Edges: {len(edge_index[0])})')
                ax2.set_xlabel('X (m)')
                ax2.set_ylabel('Y (m)')
                ax2.grid(True, alpha=0.3)

            # Plot 3: Occupancy Predictions
            ax3 = self.axes[1, 0]
            if result['predictions'] is not None:
                predictions = result['predictions']
                pos = result['graph_data'].pos.numpy()

                # Create occupancy heatmap
                scatter = ax3.scatter(pos[:, 0], pos[:, 1],
                                    c=predictions['predictions'].flatten(),
                                    cmap='RdYlBu_r', s=50, alpha=0.8,
                                    vmin=0, vmax=1)

                ax3.set_xlim(self.config.arena_bounds[0], self.config.arena_bounds[1])
                ax3.set_ylim(self.config.arena_bounds[2], self.config.arena_bounds[3])
                ax3.set_title('Occupancy Predictions')
                ax3.set_xlabel('X (m)')
                ax3.set_ylabel('Y (m)')
                ax3.grid(True, alpha=0.3)

                # Add colorbar
                plt.colorbar(scatter, ax=ax3, label='Occupancy Probability')

            # Plot 4: Processing Performance
            ax4 = self.axes[1, 1]
            if len(self.processing_times) > 0:
                times = self.processing_times[-50:]  # Last 50 frames
                ax4.plot(times, 'b-', linewidth=2)
                ax4.set_title('Processing Latency')
                ax4.set_xlabel('Frame')
                ax4.set_ylabel('Time (ms)')
                ax4.grid(True, alpha=0.3)

                # Add performance statistics
                avg_time = np.mean(times)
                ax4.text(0.02, 0.98, f'Avg: {avg_time:.1f}ms\nLast: {times[-1]:.1f}ms',
                        transform=ax4.transAxes, verticalalignment='top',
                        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

            plt.tight_layout()
            plt.pause(0.01)  # Small pause for real-time update

        except Exception as e:
            self.get_logger().error(f"Error updating visualization: {e}")

    def _print_performance_metrics(self, result):
        """Print simple latency summary"""
        times = result['processing_times']
        total_time = times['total']

        # Print simple timing summary
        print(f"TOTAL: {total_time:.1f}ms")

        if result['predictions'] is not None:
            pred_count = result['predictions']['node_count']
            avg_occupancy = np.mean(result['predictions']['predictions'])
            print(f"Nodes: {pred_count} | Avg Occupancy: {avg_occupancy:.3f}")

    def cleanup_old_data(self):
        """Clean up old data from buffers to prevent memory issues"""
        current_time = time.time()
        cutoff_time = current_time - 5.0  # Keep last 5 seconds

        for robot_id in self.sensor_data_buffer:
            self.sensor_data_buffer[robot_id] = [
                data for data in self.sensor_data_buffer[robot_id]
                if data['timestamp'] > cutoff_time
            ]

        # Keep only recent processed frames
        if len(self.processed_frames) > 100:
            self.processed_frames = self.processed_frames[-50:]

        if len(self.processing_times) > 100:
            self.processing_times = self.processing_times[-50:]


def extract_rosbag_metadata(rosbag_path):
    """Extract metadata from ROS bag for processing setup"""
    db3_files = list(Path(rosbag_path).glob("*.db3"))
    if not db3_files:
        raise FileNotFoundError(f"No .db3 files found in {rosbag_path}")

    db3_file = db3_files[0]

    # Connect to SQLite database
    conn = sqlite3.connect(str(db3_file))
    cursor = conn.cursor()

    # Get topic information
    cursor.execute("SELECT name, type FROM topics")
    topics = cursor.fetchall()

    # Get message counts
    cursor.execute("""
        SELECT topics.name, COUNT(messages.id) as message_count
        FROM topics
        LEFT JOIN messages ON topics.id = messages.topic_id
        GROUP BY topics.name
    """)
    message_counts = dict(cursor.fetchall())

    # Get time range
    cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM messages")
    time_range = cursor.fetchone()

    conn.close()

    return {
        'topics': topics,
        'message_counts': message_counts,
        'time_range': time_range,
        'duration_seconds': (time_range[1] - time_range[0]) / 1e9 if time_range[0] else 0
    }


def start_rosbag_playback(rosbag_path, rate=1.0):
    """Start ROS bag playback process"""
    cmd = ['ros2', 'bag', 'play', rosbag_path, '--rate', str(rate)]

    print(f"Starting ROS bag playback: {' '.join(cmd)}")
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )

    return process


def main():
    """Main execution function"""
    import argparse

    parser = argparse.ArgumentParser(
        description="Live ROS Bag Processor for Collaborative Perception",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --rosbag /path/to/rosbag --model models/checkpoints_standard_gatv2_t3/model_temporal_3_best.pt
  %(prog)s --rosbag /path/to/rosbag --model models/checkpoints_ecc_t3/model_temporal_3_best.pt --no-viz
        """
    )

    parser.add_argument(
        '--rosbag', type=str, required=True,
        help='Path to ROS bag directory'
    )

    parser.add_argument(
        '--model', type=str, required=True,
        help='Path to trained model checkpoint'
    )

    parser.add_argument(
        '--config', type=str, default=None,
        help='Path to model config file (auto-detected if not provided)'
    )

    parser.add_argument(
        '--voxel-size', type=float, default=0.1,
        help='Voxel size for graph conversion (default: 0.1m)'
    )

    parser.add_argument(
        '--temporal-window', type=int, default=3,
        help='Temporal window size (default: 3)'
    )

    parser.add_argument(
        '--no-viz', action='store_true',
        help='Disable real-time visualization'
    )

    parser.add_argument(
        '--quiet', action='store_true',
        help='Disable verbose timing output (only show summary)'
    )

    parser.add_argument(
        '--playback-rate', type=float, default=1.0,
        help='ROS bag playback rate (default: 1.0x)'
    )

    parser.add_argument(
        '--arena-bounds', type=float, nargs=4,
        default=[0, 20.7, 0, 9.92],
        help='Arena bounds: x_min x_max y_min y_max (default: warehouse dimensions)'
    )

    args = parser.parse_args()

    # Validate inputs
    if not os.path.exists(args.rosbag):
        print(f"Error: ROS bag path does not exist: {args.rosbag}")
        return 1

    if not os.path.exists(args.model):
        print(f"Error: Model file does not exist: {args.model}")
        return 1

    # Auto-detect config file if not provided
    if args.config is None:
        model_dir = os.path.dirname(args.model)
        config_path = os.path.join(model_dir, 'config.yaml')
        if os.path.exists(config_path):
            args.config = config_path
        else:
            print(f"Error: Could not find config.yaml in {model_dir}")
            return 1

    # Extract ROS bag metadata
    print("Analyzing ROS bag...")
    try:
        metadata = extract_rosbag_metadata(args.rosbag)
        print(f"✓ ROS bag duration: {metadata['duration_seconds']:.1f} seconds")
        print(f"✓ Available topics: {len(metadata['topics'])}")

        # Check for required topics
        topic_names = [topic[0] for topic in metadata['topics']]
        radar_topics = [t for t in topic_names if 'radar_scan_pcl' in t]
        vicon_topics = [t for t in topic_names if 'vicon/pose' in t]

        print(f"✓ Radar topics found: {len(radar_topics)}")
        print(f"✓ Vicon topics found: {len(vicon_topics)}")

        if not radar_topics:
            print("Warning: No radar topics found in ROS bag")

    except Exception as e:
        print(f"Error analyzing ROS bag: {e}")
        return 1

    # Create processing configuration
    config = ProcessingConfig(
        rosbag_path=args.rosbag,
        model_path=args.model,
        config_path=args.config,
        voxel_size=args.voxel_size,
        temporal_window=args.temporal_window,
        visualization=not args.no_viz,
        verbose_timing=not args.quiet,
        arena_bounds=tuple(args.arena_bounds)
    )

    # Initialize ROS
    rclpy.init()

    try:
        # Create processor
        print("Initializing live processor...")
        processor = LiveROSBagProcessor(config)

        # Start ROS bag playback
        print("Starting ROS bag playback...")
        playback_process = start_rosbag_playback(args.rosbag, args.playback_rate)

        # Start cleanup timer
        _ = processor.create_timer(5.0, processor.cleanup_old_data)

        print("\n" + "="*60)
        print("LIVE COLLABORATIVE PERCEPTION PROCESSING STARTED")
        print("="*60)
        print(f"ROS Bag: {args.rosbag}")
        print(f"Model: {args.model}")
        print(f"Voxel Size: {args.voxel_size}m")
        print(f"Visualization: {'Enabled' if config.visualization else 'Disabled'}")
        print("="*60)
        print("Press Ctrl+C to stop processing...")
        print()

        # Run ROS node
        rclpy.spin(processor)

    except KeyboardInterrupt:
        print("\nShutting down...")

    except Exception as e:
        print(f"Error during processing: {e}")
        return 1

    finally:
        # Cleanup
        if 'playback_process' in locals():
            playback_process.terminate()
            playback_process.wait()

        if 'processor' in locals():
            processor.destroy_node()

        rclpy.shutdown()

        if config.visualization:
            plt.close('all')

    return 0


if __name__ == '__main__':
    exit(main())
