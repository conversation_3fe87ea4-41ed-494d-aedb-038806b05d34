# Robotics Dataset - Multi-Layout Multi-Robot System

## Overview

This dataset contains comprehensive robotics data collected from a multi-robot system across three different layouts. The data includes radar point clouds, motion capture data, and workspace configuration information for robotics research and development.

## Dataset Structure

```
data_full/
├── 01_extracted/          # Raw extracted sensor data
├── 02_synchronized/       # Time-synchronized multi-sensor data
├── 03_transformed/        # Coordinate-transformed data
├── 04_cleaned/           # Noise-filtered and cleaned data
├── 05_labelled/          # Annotated data for machine learning
├── graph_frames/          # Graph neural network frames for collaborative perception
│   ├── train/            # Training set (42,936 frames, 72.1%)
│   ├── val/              # Validation set (8,802 frames, 14.8%)
│   ├── test/             # Test set (7,803 frames, 13.1%)
│   ├── split_statistics.txt    # Human-readable split information
│   └── split_statistics.json   # Machine-readable split metadata
└── README.md             # This file
```

## Processing Pipeline

The dataset follows a systematic 5-stage processing pipeline:

1. **Extraction (01_extracted)**: Raw sensor data extraction from ROS bags
2. **Synchronization (02_synchronized)**: Temporal alignment of multi-sensor streams
3. **Transformation (03_transformed)**: Coordinate system transformations and calibration
4. **Cleaning (04_cleaned)**: Noise filtering and boundary detection
5. **Labelling (05_labelled)**: Ground truth annotations for ML applications

## Layout Configurations

### Layout_01
- **Sessions**: 20 datasets (dataset_20250219_113400 to dataset_20250306_124800)
- **Robot Configurations**: Standard, Robot 1 Vertical, Robot 1 Horizontal, Robot 1 Diagonal
- **Movement Patterns**: Horizontal, Vertical, Diagonal trajectories
- **Anchor Stations**: AS_1, AS_3, AS_4, AS_5, AS_6

### Layout_02  
- **Sessions**: 9 datasets (dataset_144830 to dataset_162330)
- **Robot Configurations**: Standard configuration
- **Movement Patterns**: Horizontal, Vertical, Mixed patterns, Docking, Random
- **Anchor Stations**: AS_1, AS_3, AS_4, AS_5, AS_6

### Layout_03
- **Sessions**: 5 datasets (dataset_164720 to dataset_174030)  
- **Robot Configurations**: Standard configuration
- **Movement Patterns**: Horizontal, Vertical, Mixed patterns, Random
- **Anchor Stations**: AS_1, AS_3, AS_4, AS_5, AS_6 + KLT tracking system

## File Structure

### Extracted Data (01_extracted)
Each layout contains:
```
Layout_XX/
├── dataset_YYYYMMDD_HHMMSS/
│   ├── robot1_radar_points.csv      # Robot 1 radar point cloud data
│   ├── robot2_radar_points.csv      # Robot 2 radar point cloud data
│   └── vicon_motion_capture.csv     # Motion capture ground truth
└── layoutX_edges.csv                # Workspace boundary definitions
```

### Processed Data (02_synchronized through 05_labelled)
Each processing stage contains:
```
Layout_XX/
├── dataset_YYYYMMDD_HHMMSS_synchronized.csv    # Stage 2
├── transformed_dataset_YYYYMMDD_HHMMSS.csv     # Stage 3  
├── cleaned_dataset_YYYYMMDD_HHMMSS.csv         # Stage 4
└── annotated_dataset_YYYYMMDD_HHMMSS.csv       # Stage 5
```

## Data Formats

### Radar Point Cloud Data
- **Format**: CSV with columns [timestamp, x, y, z, intensity, ...]
- **Coordinate System**: Local robot frame
- **Frequency**: Variable (typically 10-20 Hz)

### Motion Capture Data  
- **Format**: CSV with columns [timestamp, x, y, z, qx, qy, qz, qw, ...]
- **Coordinate System**: Global Vicon frame
- **Frequency**: 100 Hz (downsampled as needed)

### Layout Edges
- **Format**: CSV with anchor station boundary coordinates
- **Columns**: AS_X_[bottom|right|top|left]_[x1|y1|x2|y2]
- **Purpose**: Workspace boundary definition for path planning

## Dataset Statistics

| Layout | Sessions  | Date Range | Robot Configs |
|--------|----------|------------|---------------|
| Layout_01 | 20  | Feb-Mar 2025 | 4 types |
| Layout_02 | 9  | May 2025 | Standard |
| Layout_03 | 5 | May 2025 | Standard |

## Graph Frames for Collaborative Perception

### Overview
The dataset includes **59,541 graph neural network frames** designed for collaborative perception research. These frames represent temporal multi-robot sensor data as graph structures, enabling advanced machine learning approaches for multi-agent systems.

### Graph Frame Structure
```
graph_frames/
├── train/                    # Training set (22 datasets, 42,936 frames)
│   ├── temporal_1/          # Highest temporal resolution (14,356 frames)
│   ├── temporal_3/          # Medium temporal resolution (14,312 frames)
│   └── temporal_5/          # Lower temporal resolution (14,268 frames)
├── val/                     # Validation set (5 datasets, 8,802 frames)
│   ├── temporal_1/          # Multi-scale validation frames
│   ├── temporal_3/          # Temporal validation data
│   └── temporal_5/          # Coarse temporal validation
├── test/                    # Test set (7 datasets, 7,803 frames)
│   ├── temporal_1/          # High-resolution test frames
│   ├── temporal_3/          # Medium-resolution test frames
│   └── temporal_5/          # Low-resolution test frames
├── split_statistics.txt     # Human-readable split information
└── split_statistics.json    # Machine-readable metadata
```

### Temporal Multi-Scale Design
- **temporal_1**: Highest temporal resolution for fine-grained analysis
- **temporal_3**: Medium temporal resolution for balanced performance
- **temporal_5**: Lower temporal resolution for computational efficiency

### Frame Format
- **File Format**: PyTorch tensors (`.pt` files)
- **Naming Convention**: `{timestamp}.pt` (Unix timestamp with microseconds)
- **Content**: Graph representations of multi-robot sensor data
- **Node Features**: Robot poses, sensor measurements, spatial relationships
- **Edge Features**: Inter-robot communication, spatial proximity, temporal connections

### Dataset Splits

#### Training Set (72.1% - 42,936 frames)
**Layout_01 (14 datasets):**
- dataset_20250219_174900, dataset_20250306_123000, dataset_20250221_111900
- dataset_20250221_124800, dataset_20250306_124600, dataset_20250221_121100
- dataset_20250306_120600, dataset_20250219_121000, dataset_20250221_122700
- dataset_20250306_115400, dataset_20250221_120000, dataset_20250306_111700
- dataset_20250221_104600, dataset_20250306_112000

**Layout_02 (6 datasets):**
- dataset_145530, dataset_162330, dataset_155450
- dataset_151420, dataset_144830, dataset_160600

**Layout_03 (2 datasets):**
- dataset_164720, dataset_173220

#### Validation Set (14.8% - 8,802 frames)
**Layout_01 (3 datasets):**
- dataset_20250306_124800, dataset_20250219_180400, dataset_20250219_113400

**Layout_02 (1 dataset):**
- dataset_154310

**Layout_03 (1 dataset):**
- dataset_174030

#### Test Set (13.1% - 7,803 frames)
**Layout_01 (3 datasets):**
- dataset_20250219_171800, dataset_20250219_120000, dataset_20250221_125900

**Layout_02 (2 datasets):**
- dataset_150340, dataset_161700

**Layout_03 (2 datasets):**
- dataset_170610, dataset_165730

### Research Applications
The graph frames enable research in:
- **Collaborative Perception**: Multi-robot sensor fusion and information sharing
- **Graph Neural Networks**: Spatial-temporal graph learning on robotic systems
- **Multi-Agent Systems**: Distributed decision making and coordination
- **SLAM and Localization**: Graph-based simultaneous localization and mapping
- **Swarm Robotics**: Large-scale multi-robot coordination algorithms

## Usage Examples

### Loading Traditional Sensor Data
```python
import pandas as pd

# Load robot radar data
robot1_data = pd.read_csv('01_extracted/Layout_01/dataset_20250219_113400/robot1_radar_points.csv')
robot2_data = pd.read_csv('01_extracted/Layout_01/dataset_20250219_113400/robot2_radar_points.csv')

# Load motion capture ground truth
vicon_data = pd.read_csv('01_extracted/Layout_01/dataset_20250219_113400/vicon_motion_capture.csv')
```

### Loading Processed Data
```python
# Load synchronized data
sync_data = pd.read_csv('02_synchronized/Layout_01/dataset_20250219_113400_synchronized.csv')

# Load cleaned data for analysis
clean_data = pd.read_csv('04_cleaned/Layout_01/cleaned_dataset_20250219_113400.csv')

# Load annotated data for ML
labeled_data = pd.read_csv('05_labelled/Layout_01/annotated_dataset_20250219_113400.csv')
```

### Loading Graph Frames for Deep Learning
```python
import torch
import os
from torch_geometric.data import Data

# Load training graph frames
train_dir = 'graph_frames/train/temporal_1/'
train_frames = []

for filename in sorted(os.listdir(train_dir)):
    if filename.endswith('.pt'):
        frame_path = os.path.join(train_dir, filename)
        graph_data = torch.load(frame_path)
        train_frames.append(graph_data)

print(f"Loaded {len(train_frames)} training graph frames")

# Load validation frames
val_dir = 'graph_frames/val/temporal_1/'
val_frames = [torch.load(os.path.join(val_dir, f))
              for f in os.listdir(val_dir) if f.endswith('.pt')]

# Load test frames
test_dir = 'graph_frames/test/temporal_1/'
test_frames = [torch.load(os.path.join(test_dir, f))
               for f in os.listdir(test_dir) if f.endswith('.pt')]
```

### Loading Split Statistics
```python
import json

# Load split information
with open('graph_frames/split_statistics.json', 'r') as f:
    split_info = json.load(f)

print(f"Training datasets: {len(split_info['dataset_assignments']['train'])}")
print(f"Validation datasets: {len(split_info['dataset_assignments']['val'])}")
print(f"Test datasets: {len(split_info['dataset_assignments']['test'])}")

# Get frame counts by temporal resolution
for split in ['train', 'val', 'test']:
    for temporal in ['temporal_1', 'temporal_3', 'temporal_5']:
        count = split_info['frame_counts'][split][temporal]
        print(f"{split} {temporal}: {count} frames")
```

### Loading Workspace Configuration
```python
# Load layout boundaries
layout1_edges = pd.read_csv('01_extracted/Layout_01/layout1_edges.csv')
layout2_edges = pd.read_csv('01_extracted/Layout_02/layout2_edges.csv')
layout3_edges = pd.read_csv('01_extracted/Layout_03/layout3_edges.csv')
```

## Applications

This comprehensive dataset is suitable for:

### Traditional Robotics Research
- **Multi-robot coordination** algorithm development
- **SLAM algorithm** development and evaluation
- **Sensor fusion** technique validation
- **Path planning** algorithm testing
- **Localization and mapping** benchmarking

### Machine Learning & AI
- **Graph Neural Networks** for robotics applications
- **Collaborative perception** model training
- **Multi-agent reinforcement learning**
- **Temporal sequence modeling** for robot behavior
- **Attention mechanisms** for multi-robot systems

### Advanced Research Areas
- **Distributed SLAM** with graph-based approaches
- **Swarm intelligence** and emergent behavior
- **Communication-efficient** multi-robot systems
- **Federated learning** for robotics
- **Transfer learning** across different layouts and configurations

## Data Quality & Validation

### Quality Assurance
- ✅ **File Integrity**: All CSV and PyTorch files validated for proper format
- ✅ **Temporal Consistency**: Graph frames properly synchronized with sensor data
- ✅ **Spatial Alignment**: Multi-robot data streams properly coordinated
- ✅ **Graph Structure**: Node and edge features validated across all frames
- ✅ **Split Integrity**: No data leakage between train/validation/test sets

### Dataset Completeness
- **59,541 total graph frames** across all temporal resolutions
- **34 unique datasets** from 3 different layout configurations
- **Multiple robot configurations** and movement patterns
- **Comprehensive sensor coverage** (radar + motion capture)
- **Professional ML splits** with proper stratification

## Citation

If you use this dataset in your research, please cite:
```
[Your citation format here - Multi-Robot Collaborative Perception Dataset]
```

## Contact & License

For questions about this dataset, please contact: [Your contact information]

License: [Specify your license terms]

## Technical Notes

- **Backup Available**: Original dataset backed up at `../data_full_backup_20250701_105513/`
- **Processing Tools**: Compatible with PyTorch, PyTorch Geometric, ROS, Python pandas, MATLAB
- **File Encoding**: UTF-8 CSV format, PyTorch tensor format for graph frames
- **Coordinate Systems**: Right-handed coordinate systems throughout
- **Time Format**: Unix timestamps with microsecond precision
- **Graph Framework**: Compatible with PyTorch Geometric and DGL
- **Hardware Requirements**: GPU recommended for graph neural network training
